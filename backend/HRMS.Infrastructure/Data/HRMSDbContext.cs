using Microsoft.EntityFrameworkCore;
using HRMS.Core.Entities;
using HRMS.Core.Common;

namespace HRMS.Infrastructure.Data;

public class HRMSDbContext : DbContext
{
    private readonly string _schema;

    public HRMSDbContext(DbContextOptions<HRMSDbContext> options, string schema = "dbo") : base(options)
    {
        _schema = schema;
    }

    // DbSets for all entities
    public DbSet<Organization> Organizations { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<EmployeeDetail> EmployeeDetails { get; set; }
    public DbSet<EmployeeEducation> EmployeeEducation { get; set; }
    public DbSet<EmployeeSkill> EmployeeSkills { get; set; }
    public DbSet<AttendanceRecord> AttendanceRecords { get; set; }
    public DbSet<LeaveType> LeaveTypes { get; set; }
    public DbSet<LeaveBalance> LeaveBalances { get; set; }
    public DbSet<LeaveRequest> LeaveRequests { get; set; }
    public DbSet<MonthlyLeaveAllowance> MonthlyLeaveAllowances { get; set; }
    public DbSet<MonthlyLeaveUsage> MonthlyLeaveUsages { get; set; }
    public DbSet<HRMS.Core.Entities.Task> Tasks { get; set; }
    public DbSet<TaskUpdate> TaskUpdates { get; set; }
    public DbSet<TaskComment> TaskComments { get; set; }
    public DbSet<PerformanceReview> PerformanceReviews { get; set; }
    public DbSet<PerformanceGoal> PerformanceGoals { get; set; }
    public DbSet<OrganizationSetting> OrganizationSettings { get; set; }
    public DbSet<JobPosting> JobPostings { get; set; }
    public DbSet<JobApplication> JobApplications { get; set; }

    // New entities for comprehensive employee management
    public DbSet<Permission> Permissions { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<RolePermission> RolePermissions { get; set; }
    public DbSet<EmployeeRole> EmployeeRoles { get; set; }
    public DbSet<EmployeePermission> EmployeePermissions { get; set; }
    public DbSet<Department> Departments { get; set; }
    public DbSet<DepartmentEmployee> DepartmentEmployees { get; set; }
    public DbSet<Position> Positions { get; set; }
    public DbSet<EmployeePosition> EmployeePositions { get; set; }
    public DbSet<PayrollCycle> PayrollCycles { get; set; }
    public DbSet<EmployeePayroll> EmployeePayrolls { get; set; }
    public DbSet<SalaryComponent> SalaryComponents { get; set; }
    public DbSet<EmployeeSalaryStructure> EmployeeSalaryStructures { get; set; }
    public DbSet<PayrollComponent> PayrollComponents { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Set default schema
        modelBuilder.HasDefaultSchema(_schema);

        // Configure entities
        ConfigureOrganization(modelBuilder);
        ConfigureUser(modelBuilder);
        ConfigureEmployeeDetail(modelBuilder);
        ConfigureAttendance(modelBuilder);
        ConfigureLeave(modelBuilder);
        ConfigureMonthlyLeave(modelBuilder);
        ConfigureTask(modelBuilder);
        ConfigurePerformance(modelBuilder);
        ConfigureSettings(modelBuilder);
        ConfigureRecruitment(modelBuilder);
        ConfigurePermissions(modelBuilder);
        ConfigureDepartments(modelBuilder);
        ConfigurePayroll(modelBuilder);

        // Configure audit fields for all entities
        ConfigureAuditFields(modelBuilder);
    }

    private void ConfigureOrganization(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Organization>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(255);
            entity.Property(e => e.Domain).IsRequired().HasMaxLength(255);
            entity.HasIndex(e => e.Domain).IsUnique();
            entity.Property(e => e.Industry).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Status).HasConversion<string>();
            entity.Property(e => e.MonthlyRevenue).HasColumnType("decimal(12,2)");
        });
    }

    private void ConfigureUser(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Email).IsRequired().HasMaxLength(255);
            entity.HasIndex(e => e.Email).IsUnique();
            entity.Property(e => e.Name).IsRequired().HasMaxLength(255);
            entity.Property(e => e.Role).HasConversion<string>();
            entity.Property(e => e.AvatarUrl).HasMaxLength(500);

            entity.HasOne(e => e.Organization)
                  .WithMany(o => o.Users)
                  .HasForeignKey(e => e.OrganizationId)
                  .OnDelete(DeleteBehavior.Restrict);
        });
    }

    private void ConfigureEmployeeDetail(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<EmployeeDetail>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.EmployeeId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.JobTitle).IsRequired().HasMaxLength(255);
            entity.Property(e => e.Department).IsRequired().HasMaxLength(100);
            entity.Property(e => e.EmploymentType).HasConversion<string>();
            entity.Property(e => e.EmploymentStatus).HasConversion<string>();
            entity.Property(e => e.BaseSalary).HasColumnType("decimal(12,2)");
            entity.Property(e => e.AnnualCTC).HasColumnType("decimal(12,2)");
            entity.Property(e => e.PerformanceRating).HasColumnType("decimal(3,2)");

            entity.HasOne(e => e.User)
                  .WithOne(u => u.EmployeeDetail)
                  .HasForeignKey<EmployeeDetail>(e => e.UserId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.Manager)
                  .WithMany()
                  .HasForeignKey(e => e.ManagerId)
                  .OnDelete(DeleteBehavior.SetNull);
        });

        modelBuilder.Entity<EmployeeEducation>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.EmployeeDetail)
                  .WithMany(ed => ed.Education)
                  .HasForeignKey(e => e.EmployeeDetailId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<EmployeeSkill>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.EmployeeDetail)
                  .WithMany(ed => ed.Skills)
                  .HasForeignKey(e => e.EmployeeDetailId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private void ConfigureAttendance(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<AttendanceRecord>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Status).HasConversion<string>();
            entity.Property(e => e.TotalHours).HasColumnType("decimal(4,2)");

            entity.HasOne(e => e.User)
                  .WithMany(u => u.AttendanceRecords)
                  .HasForeignKey(e => e.UserId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasIndex(e => new { e.UserId, e.Date }).IsUnique();
        });
    }

    private void ConfigureLeave(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<LeaveType>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Organization)
                  .WithMany()
                  .HasForeignKey(e => e.OrganizationId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<LeaveBalance>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.User)
                  .WithMany()
                  .HasForeignKey(e => e.UserId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.LeaveType)
                  .WithMany(lt => lt.LeaveBalances)
                  .HasForeignKey(e => e.LeaveTypeId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasIndex(e => new { e.UserId, e.LeaveTypeId, e.Year }).IsUnique();
        });

        modelBuilder.Entity<LeaveRequest>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Status).HasConversion<string>();

            entity.HasOne(e => e.User)
                  .WithMany(u => u.LeaveRequests)
                  .HasForeignKey(e => e.UserId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.LeaveType)
                  .WithMany(lt => lt.LeaveRequests)
                  .HasForeignKey(e => e.LeaveTypeId)
                  .OnDelete(DeleteBehavior.SetNull)
                  .IsRequired(false); // Make LeaveType optional for simplified monthly system

            entity.HasOne(e => e.Approver)
                  .WithMany()
                  .HasForeignKey(e => e.ApprovedBy)
                  .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private void ConfigureTask(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<HRMS.Core.Entities.Task>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Priority).HasConversion<string>();
            entity.Property(e => e.Status).HasConversion<string>();
            entity.Property(e => e.TaskType).HasConversion<string>();
            entity.Property(e => e.EstimatedHours).HasColumnType("decimal(6,2)");
            entity.Property(e => e.ActualHours).HasColumnType("decimal(6,2)");

            entity.HasOne(e => e.AssignedTo)
                  .WithMany(u => u.AssignedTasks)
                  .HasForeignKey(e => e.AssignedToId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.AssignedBy)
                  .WithMany(u => u.CreatedTasks)
                  .HasForeignKey(e => e.AssignedById)
                  .OnDelete(DeleteBehavior.Restrict);
        });

        modelBuilder.Entity<TaskUpdate>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Status).HasConversion<string>();
            entity.Property(e => e.UpdateType).HasConversion<string>();
            entity.Property(e => e.HoursSpent).HasColumnType("decimal(4,2)");
            entity.Property(e => e.UpdateDate).HasColumnType("date");

            entity.HasOne(e => e.Task)
                  .WithMany(t => t.TaskUpdates)
                  .HasForeignKey(e => e.TaskId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.UpdatedBy)
                  .WithMany()
                  .HasForeignKey(e => e.UpdatedById)
                  .OnDelete(DeleteBehavior.Restrict);
        });

        modelBuilder.Entity<TaskComment>(entity =>
        {
            entity.HasKey(e => e.Id);

            entity.HasOne(e => e.Task)
                  .WithMany(t => t.TaskComments)
                  .HasForeignKey(e => e.TaskId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Author)
                  .WithMany()
                  .HasForeignKey(e => e.AuthorId)
                  .OnDelete(DeleteBehavior.Restrict);
        });
    }

    private void ConfigurePerformance(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<PerformanceReview>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Status).HasConversion<string>();
            entity.Property(e => e.OverallRating).HasColumnType("decimal(3,2)");

            entity.HasOne(e => e.Employee)
                  .WithMany(u => u.PerformanceReviews)
                  .HasForeignKey(e => e.EmployeeId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.Reviewer)
                  .WithMany()
                  .HasForeignKey(e => e.ReviewerId)
                  .OnDelete(DeleteBehavior.Restrict);
        });

        modelBuilder.Entity<PerformanceGoal>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.AchievedRating).HasColumnType("decimal(3,2)");

            entity.HasOne(e => e.Review)
                  .WithMany(r => r.Goals)
                  .HasForeignKey(e => e.ReviewId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private void ConfigureSettings(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<OrganizationSetting>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Organization)
                  .WithMany(o => o.Settings)
                  .HasForeignKey(e => e.OrganizationId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasIndex(e => new { e.OrganizationId, e.Key }).IsUnique();
        });
    }

    private void ConfigureRecruitment(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<JobPosting>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.EmploymentType).HasConversion<string>();
            entity.Property(e => e.Status).HasConversion<string>();
            entity.Property(e => e.SalaryRangeMin).HasColumnType("decimal(12,2)");
            entity.Property(e => e.SalaryRangeMax).HasColumnType("decimal(12,2)");

            entity.HasOne(e => e.PostedBy)
                  .WithMany()
                  .HasForeignKey(e => e.PostedById)
                  .OnDelete(DeleteBehavior.Restrict);
        });

        modelBuilder.Entity<JobApplication>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Status).HasConversion<string>();
            entity.Property(e => e.CurrentSalary).HasColumnType("decimal(12,2)");
            entity.Property(e => e.ExpectedSalary).HasColumnType("decimal(12,2)");

            entity.HasOne(e => e.JobPosting)
                  .WithMany(jp => jp.Applications)
                  .HasForeignKey(e => e.JobPostingId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasIndex(e => new { e.JobPostingId, e.CandidateEmail }).IsUnique();
        });
    }

    private void ConfigureAuditFields(ModelBuilder modelBuilder)
    {
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            if (typeof(BaseAuditableEntity).IsAssignableFrom(entityType.ClrType))
            {
                modelBuilder.Entity(entityType.ClrType)
                    .Property<DateTime>("CreatedAt")
                    .HasDefaultValueSql("GETUTCDATE()");

                modelBuilder.Entity(entityType.ClrType)
                    .Property<DateTime>("UpdatedAt")
                    .HasDefaultValueSql("GETUTCDATE()");
            }
        }
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateAuditFields();
        return await base.SaveChangesAsync(cancellationToken);
    }

    private void ConfigurePermissions(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Permission>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.Code).IsUnique();
            entity.HasIndex(e => new { e.Module, e.Action });
        });

        modelBuilder.Entity<Role>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Organization)
                  .WithMany()
                  .HasForeignKey(e => e.OrganizationId)
                  .OnDelete(DeleteBehavior.Cascade);
            entity.HasIndex(e => new { e.OrganizationId, e.Name }).IsUnique();
        });

        modelBuilder.Entity<RolePermission>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Role)
                  .WithMany(r => r.RolePermissions)
                  .HasForeignKey(e => e.RoleId)
                  .OnDelete(DeleteBehavior.Cascade);
            entity.HasOne(e => e.Permission)
                  .WithMany(p => p.RolePermissions)
                  .HasForeignKey(e => e.PermissionId)
                  .OnDelete(DeleteBehavior.Cascade);
            entity.HasIndex(e => new { e.RoleId, e.PermissionId }).IsUnique();
        });

        modelBuilder.Entity<EmployeeRole>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.EmployeeDetail)
                  .WithMany(ed => ed.EmployeeRoles)
                  .HasForeignKey(e => e.EmployeeDetailId)
                  .OnDelete(DeleteBehavior.Cascade);
            entity.HasOne(e => e.Role)
                  .WithMany(r => r.EmployeeRoles)
                  .HasForeignKey(e => e.RoleId)
                  .OnDelete(DeleteBehavior.Restrict);
        });

        modelBuilder.Entity<EmployeePermission>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.EmployeeDetail)
                  .WithMany()
                  .HasForeignKey(e => e.EmployeeDetailId)
                  .OnDelete(DeleteBehavior.Cascade);
            entity.HasOne(e => e.Permission)
                  .WithMany(p => p.EmployeePermissions)
                  .HasForeignKey(e => e.PermissionId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private void ConfigureDepartments(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Department>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Organization)
                  .WithMany()
                  .HasForeignKey(e => e.OrganizationId)
                  .OnDelete(DeleteBehavior.Cascade);
            entity.HasOne(e => e.ParentDepartment)
                  .WithMany(d => d.SubDepartments)
                  .HasForeignKey(e => e.ParentDepartmentId)
                  .OnDelete(DeleteBehavior.Restrict);
            entity.HasOne(e => e.HeadOfDepartment)
                  .WithMany()
                  .HasForeignKey(e => e.HeadOfDepartmentId)
                  .OnDelete(DeleteBehavior.SetNull);
            entity.HasIndex(e => new { e.OrganizationId, e.Code }).IsUnique();
            entity.Property(e => e.Budget).HasColumnType("decimal(15,2)");
        });

        modelBuilder.Entity<DepartmentEmployee>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Department)
                  .WithMany(d => d.DepartmentEmployees)
                  .HasForeignKey(e => e.DepartmentId)
                  .OnDelete(DeleteBehavior.Cascade);
            entity.HasOne(e => e.EmployeeDetail)
                  .WithMany()
                  .HasForeignKey(e => e.EmployeeDetailId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<Position>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Organization)
                  .WithMany()
                  .HasForeignKey(e => e.OrganizationId)
                  .OnDelete(DeleteBehavior.Cascade);
            entity.HasOne(e => e.Department)
                  .WithMany()
                  .HasForeignKey(e => e.DepartmentId)
                  .OnDelete(DeleteBehavior.Cascade);
            entity.HasIndex(e => new { e.OrganizationId, e.Code }).IsUnique();
            entity.Property(e => e.MinSalary).HasColumnType("decimal(12,2)");
            entity.Property(e => e.MaxSalary).HasColumnType("decimal(12,2)");
        });

        modelBuilder.Entity<EmployeePosition>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.EmployeeDetail)
                  .WithMany()
                  .HasForeignKey(e => e.EmployeeDetailId)
                  .OnDelete(DeleteBehavior.Cascade);
            entity.HasOne(e => e.Position)
                  .WithMany(p => p.EmployeePositions)
                  .HasForeignKey(e => e.PositionId)
                  .OnDelete(DeleteBehavior.Restrict);
        });
    }

    private void ConfigurePayroll(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<PayrollCycle>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Organization)
                  .WithMany()
                  .HasForeignKey(e => e.OrganizationId)
                  .OnDelete(DeleteBehavior.Cascade);
            entity.HasOne(e => e.ProcessedByUser)
                  .WithMany()
                  .HasForeignKey(e => e.ProcessedBy)
                  .OnDelete(DeleteBehavior.SetNull);
            entity.Property(e => e.Status).HasConversion<string>();
            entity.Property(e => e.TotalGrossAmount).HasColumnType("decimal(15,2)");
            entity.Property(e => e.TotalNetAmount).HasColumnType("decimal(15,2)");
            entity.Property(e => e.TotalDeductions).HasColumnType("decimal(15,2)");
        });

        modelBuilder.Entity<EmployeePayroll>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.PayrollCycle)
                  .WithMany(pc => pc.EmployeePayrolls)
                  .HasForeignKey(e => e.PayrollCycleId)
                  .OnDelete(DeleteBehavior.Cascade);
            entity.HasOne(e => e.Employee)
                  .WithMany()
                  .HasForeignKey(e => e.EmployeeId)
                  .OnDelete(DeleteBehavior.Restrict);
            entity.Property(e => e.Status).HasConversion<string>();
            entity.Property(e => e.GrossSalary).HasColumnType("decimal(12,2)");
            entity.Property(e => e.BasicSalary).HasColumnType("decimal(12,2)");
            entity.Property(e => e.Allowances).HasColumnType("decimal(12,2)");
            entity.Property(e => e.OvertimeAmount).HasColumnType("decimal(12,2)");
            entity.Property(e => e.BonusAmount).HasColumnType("decimal(12,2)");
            entity.Property(e => e.TotalDeductions).HasColumnType("decimal(12,2)");
            entity.Property(e => e.TaxDeductions).HasColumnType("decimal(12,2)");
            entity.Property(e => e.InsuranceDeductions).HasColumnType("decimal(12,2)");
            entity.Property(e => e.ProvidentFund).HasColumnType("decimal(12,2)");
            entity.Property(e => e.OtherDeductions).HasColumnType("decimal(12,2)");
            entity.Property(e => e.NetSalary).HasColumnType("decimal(12,2)");
            entity.Property(e => e.LeaveDays).HasColumnType("decimal(4,1)");
            entity.Property(e => e.OvertimeHours).HasColumnType("decimal(6,2)");
            entity.HasIndex(e => new { e.PayrollCycleId, e.EmployeeId }).IsUnique();
        });

        modelBuilder.Entity<SalaryComponent>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Organization)
                  .WithMany()
                  .HasForeignKey(e => e.OrganizationId)
                  .OnDelete(DeleteBehavior.Cascade);
            entity.HasIndex(e => new { e.OrganizationId, e.Code }).IsUnique();
            entity.Property(e => e.DefaultValue).HasColumnType("decimal(12,2)");
            entity.Property(e => e.MinValue).HasColumnType("decimal(12,2)");
            entity.Property(e => e.MaxValue).HasColumnType("decimal(12,2)");
        });

        modelBuilder.Entity<EmployeeSalaryStructure>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Employee)
                  .WithMany()
                  .HasForeignKey(e => e.EmployeeId)
                  .OnDelete(DeleteBehavior.Cascade);
            entity.HasOne(e => e.Component)
                  .WithMany(c => c.EmployeeSalaryStructures)
                  .HasForeignKey(e => e.ComponentId)
                  .OnDelete(DeleteBehavior.Restrict);
            entity.Property(e => e.Amount).HasColumnType("decimal(12,2)");
        });

        modelBuilder.Entity<PayrollComponent>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.EmployeePayroll)
                  .WithMany(ep => ep.PayrollComponents)
                  .HasForeignKey(e => e.EmployeePayrollId)
                  .OnDelete(DeleteBehavior.Cascade);
            entity.HasOne(e => e.Component)
                  .WithMany(c => c.PayrollComponents)
                  .HasForeignKey(e => e.ComponentId)
                  .OnDelete(DeleteBehavior.Restrict);
            entity.Property(e => e.Amount).HasColumnType("decimal(12,2)");
            entity.Property(e => e.CalculatedValue).HasColumnType("decimal(12,2)");
        });
    }

    private void UpdateAuditFields()
    {
        var entries = ChangeTracker.Entries<BaseAuditableEntity>();

        foreach (var entry in entries)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = DateTime.UtcNow;
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    break;
                case EntityState.Modified:
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    break;
            }
        }
    }

    private void ConfigureMonthlyLeave(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<MonthlyLeaveAllowance>(entity =>
        {
            entity.HasKey(e => e.Id);

            entity.HasOne(e => e.User)
                  .WithMany()
                  .HasForeignKey(e => e.UserId)
                  .OnDelete(DeleteBehavior.Cascade);

            // Unique constraint for user, year, and month
            entity.HasIndex(e => new { e.UserId, e.Year, e.Month }).IsUnique();

            // Add check constraints
            entity.HasCheckConstraint("CK_MonthlyLeaveAllowance_Month", "Month >= 1 AND Month <= 12");
            entity.HasCheckConstraint("CK_MonthlyLeaveAllowance_Year", "Year >= 2020 AND Year <= 2100");
        });

        modelBuilder.Entity<MonthlyLeaveUsage>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Status).HasConversion<string>();

            entity.HasOne(e => e.LeaveRequest)
                  .WithMany()
                  .HasForeignKey(e => e.LeaveRequestId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.MonthlyLeaveAllowance)
                  .WithMany()
                  .HasForeignKey(e => e.MonthlyLeaveAllowanceId)
                  .OnDelete(DeleteBehavior.Cascade);

            // Unique constraint to prevent duplicate usage records for same leave request
            entity.HasIndex(e => e.LeaveRequestId).IsUnique();
        });
    }
}
