using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Data.SqlClient;
using HRMS.Infrastructure.Data;
using HRMS.Core.Interfaces;
using HRMS.Core.Entities;
using HRMS.Core.Enums;
using TaskEntity = HRMS.Core.Entities.Task;

namespace HRMS.Infrastructure.Services;

public class DatabaseProvisioningService : IDatabaseProvisioningService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<DatabaseProvisioningService> _logger;
    private readonly ITenantDbContextFactory _contextFactory;

    public DatabaseProvisioningService(
        IConfiguration configuration,
        ILogger<DatabaseProvisioningService> logger,
        ITenantDbContextFactory contextFactory)
    {
        _configuration = configuration;
        _logger = logger;
        _contextFactory = contextFactory;
    }

    public async System.Threading.Tasks.Task<bool> ProvisionOrganizationSchemaAsync(string organizationId)
    {
        try
        {
            _logger.LogInformation("Starting schema provisioning for organization {OrganizationId}", organizationId);

            var schemaName = GetSchemaName(organizationId);
            
            // Check if schema already exists
            if (await SchemaExistsAsync(organizationId))
            {
                _logger.LogWarning("Schema {SchemaName} already exists for organization {OrganizationId}", 
                    schemaName, organizationId);
                return true;
            }

            // Create schema
            await CreateSchemaAsync(schemaName);
            
            // Run migrations
            await MigrateSchemaAsync(organizationId);
            
            // Seed default data
            await SeedDefaultDataAsync(organizationId);

            _logger.LogInformation("Successfully provisioned schema {SchemaName} for organization {OrganizationId}", 
                schemaName, organizationId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to provision schema for organization {OrganizationId}", organizationId);
            
            // Attempt cleanup on failure
            try
            {
                await DeleteOrganizationSchemaAsync(organizationId);
            }
            catch (Exception cleanupEx)
            {
                _logger.LogError(cleanupEx, "Failed to cleanup schema after provisioning failure for organization {OrganizationId}", 
                    organizationId);
            }
            
            return false;
        }
    }

    public async System.Threading.Tasks.Task<bool> SchemaExistsAsync(string organizationId)
    {
        try
        {
            var schemaName = GetSchemaName(organizationId);
            var connectionString = GetConnectionString();

            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();

            using var command = new SqlCommand(
                "SELECT CASE WHEN EXISTS(SELECT 1 FROM sys.schemas WHERE name = @schemaName) THEN 1 ELSE 0 END",
                connection);

            command.Parameters.AddWithValue("@schemaName", schemaName);

            var result = await command.ExecuteScalarAsync();
            return Convert.ToBoolean(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if schema exists for organization {OrganizationId}", organizationId);
            return false;
        }
    }

    public async System.Threading.Tasks.Task<bool> DeleteOrganizationSchemaAsync(string organizationId)
    {
        try
        {
            var schemaName = GetSchemaName(organizationId);
            var connectionString = GetConnectionString();

            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();

            // First drop all objects in the schema, then drop the schema
            using var command = new SqlCommand($@"
                IF EXISTS (SELECT 1 FROM sys.schemas WHERE name = '{schemaName}')
                BEGIN
                    DECLARE @sql NVARCHAR(MAX) = '';
                    SELECT @sql = @sql + 'DROP TABLE [' + '{schemaName}' + '].[' + TABLE_NAME + '];' + CHAR(13)
                    FROM INFORMATION_SCHEMA.TABLES
                    WHERE TABLE_SCHEMA = '{schemaName}';
                    EXEC sp_executesql @sql;
                    DROP SCHEMA [{schemaName}];
                END", connection);
            await command.ExecuteNonQueryAsync();

            _logger.LogInformation("Successfully deleted schema {SchemaName} for organization {OrganizationId}",
                schemaName, organizationId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete schema for organization {OrganizationId}", organizationId);
            return false;
        }
    }

    public async System.Threading.Tasks.Task<bool> MigrateSchemaAsync(string organizationId)
    {
        try
        {
            _logger.LogInformation("Running migrations for organization {OrganizationId}", organizationId);

            using var context = _contextFactory.CreateDbContext(organizationId);
            await context.Database.MigrateAsync();

            _logger.LogInformation("Successfully migrated schema for organization {OrganizationId}", organizationId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to migrate schema for organization {OrganizationId}", organizationId);
            return false;
        }
    }

    private async System.Threading.Tasks.Task CreateSchemaAsync(string schemaName)
    {
        try
        {
            _logger.LogInformation("Creating schema {SchemaName}", schemaName);

            var connectionString = GetConnectionString();

            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();

            // Check if schema already exists before creating
            using var checkCommand = new SqlCommand(
                "SELECT CASE WHEN EXISTS(SELECT 1 FROM sys.schemas WHERE name = @schemaName) THEN 1 ELSE 0 END",
                connection);
            checkCommand.Parameters.AddWithValue("@schemaName", schemaName);

            var exists = Convert.ToBoolean(await checkCommand.ExecuteScalarAsync());
            if (exists)
            {
                _logger.LogInformation("Schema {SchemaName} already exists, skipping creation", schemaName);
                return;
            }

            using var command = new SqlCommand($"CREATE SCHEMA [{schemaName}]", connection);
            await command.ExecuteNonQueryAsync();

            _logger.LogInformation("Successfully created schema {SchemaName}", schemaName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create schema {SchemaName}", schemaName);
            throw;
        }
    }

    private async System.Threading.Tasks.Task SeedDefaultDataAsync(string organizationId)
    {
        try
        {
            _logger.LogInformation("Seeding default data for organization {OrganizationId}", organizationId);

            using var context = _contextFactory.CreateDbContext(organizationId);

            // Seed default leave types
            await SeedDefaultLeaveTypesAsync(context, organizationId);

            // Seed default organization settings
            await SeedDefaultOrganizationSettingsAsync(context, organizationId);

            // Seed admin user data in organization schema
            await SeedAdminUserDataAsync(context, organizationId);

            await context.SaveChangesAsync();

            _logger.LogInformation("Successfully seeded default data for organization {OrganizationId}", organizationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to seed default data for organization {OrganizationId}", organizationId);
            throw;
        }
    }

    private async System.Threading.Tasks.Task SeedDefaultLeaveTypesAsync(HRMSDbContext context, string organizationId)
    {
        var organizationGuid = Guid.Parse(organizationId);

        var defaultLeaveTypes = new[]
        {
            new HRMS.Core.Entities.LeaveType
            {
                OrganizationId = organizationGuid,
                Name = "Annual Leave",
                Description = "Yearly vacation leave",
                MaxDaysPerYear = 25,
                IsCarryForward = true,
                MaxCarryForwardDays = 5,
                IsActive = true
            },
            new HRMS.Core.Entities.LeaveType
            {
                OrganizationId = organizationGuid,
                Name = "Sick Leave",
                Description = "Medical leave",
                MaxDaysPerYear = 12,
                IsCarryForward = false,
                MaxCarryForwardDays = 0,
                IsActive = true
            },
            new HRMS.Core.Entities.LeaveType
            {
                OrganizationId = organizationGuid,
                Name = "Personal Leave",
                Description = "Personal time off",
                MaxDaysPerYear = 5,
                IsCarryForward = false,
                MaxCarryForwardDays = 0,
                IsActive = true
            }
        };

        await context.LeaveTypes.AddRangeAsync(defaultLeaveTypes);
    }

    private async System.Threading.Tasks.Task SeedDefaultOrganizationSettingsAsync(HRMSDbContext context, string organizationId)
    {
        var organizationGuid = Guid.Parse(organizationId);

        var defaultSettings = new[]
        {
            new HRMS.Core.Entities.OrganizationSetting
            {
                OrganizationId = organizationGuid,
                Key = "working_hours_per_day",
                Value = "8",
                Description = "Standard working hours per day",
                DataType = "number"
            },
            new HRMS.Core.Entities.OrganizationSetting
            {
                OrganizationId = organizationGuid,
                Key = "working_days_per_week",
                Value = "5",
                Description = "Standard working days per week",
                DataType = "number"
            },
            new HRMS.Core.Entities.OrganizationSetting
            {
                OrganizationId = organizationGuid,
                Key = "timezone",
                Value = "UTC",
                Description = "Organization timezone",
                DataType = "string"
            },
            new HRMS.Core.Entities.OrganizationSetting
            {
                OrganizationId = organizationGuid,
                Key = "currency",
                Value = "USD",
                Description = "Organization currency",
                DataType = "string"
            }
        };

        await context.OrganizationSettings.AddRangeAsync(defaultSettings);
    }

    private async System.Threading.Tasks.Task SeedAdminUserDataAsync(HRMSDbContext context, string organizationId)
    {
        var organizationGuid = Guid.Parse(organizationId);

        // Get the admin user from master database
        using var masterContext = _contextFactory.CreateMasterDbContext();
        var adminUser = await masterContext.Users
            .Include(u => u.EmployeeDetail)
            .FirstOrDefaultAsync(u => u.OrganizationId == organizationGuid && u.Role == UserRole.OrgAdmin);

        if (adminUser != null)
        {
            // Create organization record in organization schema
            var organization = await masterContext.Organizations.FindAsync(organizationGuid);
            if (organization != null)
            {
                var orgCopy = new Organization
                {
                    Id = organization.Id,
                    Name = organization.Name,
                    Domain = organization.Domain,
                    Industry = organization.Industry,
                    EmployeeCount = organization.EmployeeCount,
                    Status = organization.Status,
                    SubscriptionPlan = organization.SubscriptionPlan,
                    MonthlyRevenue = organization.MonthlyRevenue,
                    CreatedAt = organization.CreatedAt,
                    UpdatedAt = organization.UpdatedAt
                };
                await context.Organizations.AddAsync(orgCopy);
            }

            // Create admin user in organization schema
            var userCopy = new User
            {
                Id = adminUser.Id,
                Email = adminUser.Email,
                PasswordHash = adminUser.PasswordHash,
                Name = adminUser.Name,
                Role = adminUser.Role,
                OrganizationId = adminUser.OrganizationId,
                AvatarUrl = adminUser.AvatarUrl,
                IsActive = adminUser.IsActive,
                EmailVerified = adminUser.EmailVerified,
                LastLogin = adminUser.LastLogin,
                CreatedAt = adminUser.CreatedAt,
                UpdatedAt = adminUser.UpdatedAt
            };
            await context.Users.AddAsync(userCopy);

            // Create admin employee details in organization schema
            if (adminUser.EmployeeDetail != null)
            {
                var employeeDetailCopy = new EmployeeDetail
                {
                    Id = adminUser.EmployeeDetail.Id,
                    UserId = adminUser.EmployeeDetail.UserId,
                    EmployeeId = adminUser.EmployeeDetail.EmployeeId,
                    JobTitle = adminUser.EmployeeDetail.JobTitle,
                    Department = adminUser.EmployeeDetail.Department,
                    ManagerId = adminUser.EmployeeDetail.ManagerId,
                    JoinDate = adminUser.EmployeeDetail.JoinDate,
                    EmploymentType = adminUser.EmployeeDetail.EmploymentType,
                    EmploymentStatus = adminUser.EmployeeDetail.EmploymentStatus,
                    WorkLocation = adminUser.EmployeeDetail.WorkLocation,
                    Phone = adminUser.EmployeeDetail.Phone,
                    Address = adminUser.EmployeeDetail.Address,
                    DateOfBirth = adminUser.EmployeeDetail.DateOfBirth,
                    BaseSalary = adminUser.EmployeeDetail.BaseSalary,
                    AnnualCTC = adminUser.EmployeeDetail.AnnualCTC,
                    NextSalaryReview = adminUser.EmployeeDetail.NextSalaryReview,
                    PerformanceRating = adminUser.EmployeeDetail.PerformanceRating,
                    TotalExperience = adminUser.EmployeeDetail.TotalExperience,
                    EmergencyContactName = adminUser.EmployeeDetail.EmergencyContactName,
                    EmergencyContactPhone = adminUser.EmployeeDetail.EmergencyContactPhone,
                    EmergencyContactRelation = adminUser.EmployeeDetail.EmergencyContactRelation,
                    BloodGroup = adminUser.EmployeeDetail.BloodGroup,
                    MaritalStatus = adminUser.EmployeeDetail.MaritalStatus,
                    Gender = adminUser.EmployeeDetail.Gender,
                    Nationality = adminUser.EmployeeDetail.Nationality,
                    BankAccountNumber = adminUser.EmployeeDetail.BankAccountNumber,
                    BankIFSC = adminUser.EmployeeDetail.BankIFSC,
                    BankName = adminUser.EmployeeDetail.BankName,
                    PAN = adminUser.EmployeeDetail.PAN,
                    Aadhar = adminUser.EmployeeDetail.Aadhar,
                    ProbationEndDate = adminUser.EmployeeDetail.ProbationEndDate,
                    ConfirmationDate = adminUser.EmployeeDetail.ConfirmationDate,
                    ResignationDate = adminUser.EmployeeDetail.ResignationDate,
                    LastWorkingDate = adminUser.EmployeeDetail.LastWorkingDate,
                    ResignationReason = adminUser.EmployeeDetail.ResignationReason,
                    CreatedAt = adminUser.EmployeeDetail.CreatedAt,
                    UpdatedAt = adminUser.EmployeeDetail.UpdatedAt,
                    CreatedBy = adminUser.EmployeeDetail.CreatedBy,
                    UpdatedBy = adminUser.EmployeeDetail.UpdatedBy
                };
                await context.EmployeeDetails.AddAsync(employeeDetailCopy);

                // Create default leave balances for admin
                var leaveTypes = await context.LeaveTypes.Where(lt => lt.OrganizationId == organizationGuid).ToListAsync();
                var currentYear = DateTime.UtcNow.Year;

                foreach (var leaveType in leaveTypes)
                {
                    var leaveBalance = new LeaveBalance
                    {
                        UserId = adminUser.Id,
                        LeaveTypeId = leaveType.Id,
                        Year = currentYear,
                        TotalAllocated = leaveType.MaxDaysPerYear,
                        UsedLeaves = 0,
                        CarriedForward = 0
                    };
                    await context.LeaveBalances.AddAsync(leaveBalance);
                }
            }
        }
    }

    private string GetConnectionString()
    {
        var connectionString = _configuration.GetConnectionString("DefaultConnection");
        if (string.IsNullOrEmpty(connectionString))
        {
            throw new InvalidOperationException("Database connection string 'DefaultConnection' not found.");
        }

        // Replace placeholders with environment variables if they exist
        connectionString = connectionString
            .Replace("{SQL_SERVER_HOST}", Environment.GetEnvironmentVariable("SQL_SERVER_HOST") ?? "{SQL_SERVER_HOST}")
            .Replace("{SQL_SERVER_DATABASE}", Environment.GetEnvironmentVariable("SQL_SERVER_DATABASE") ?? "{SQL_SERVER_DATABASE}")
            .Replace("{SQL_SERVER_USERNAME}", Environment.GetEnvironmentVariable("SQL_SERVER_USERNAME") ?? "{SQL_SERVER_USERNAME}")
            .Replace("{SQL_SERVER_PASSWORD}", Environment.GetEnvironmentVariable("SQL_SERVER_PASSWORD") ?? "{SQL_SERVER_PASSWORD}");

        // Validate that placeholders have been replaced
        if (connectionString.Contains("{SQL_SERVER_"))
        {
            throw new InvalidOperationException("SQL Server connection string contains unresolved placeholders. Please set the required environment variables: SQL_SERVER_HOST, SQL_SERVER_DATABASE, SQL_SERVER_USERNAME, SQL_SERVER_PASSWORD");
        }

        return connectionString;
    }

    private string GetSchemaName(string organizationId)
    {
        var schemaPrefix = _configuration.GetValue<string>("Database:SchemaPrefix") ?? "org_";
        var schemaSuffix = _configuration.GetValue<string>("Database:SchemaSuffix") ?? "";
        
        return $"{schemaPrefix}{organizationId.Replace("-", "")}{schemaSuffix}".ToLowerInvariant();
    }
}
