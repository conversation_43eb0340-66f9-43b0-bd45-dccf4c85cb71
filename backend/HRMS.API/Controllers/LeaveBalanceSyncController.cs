using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HRMS.Application.Common;
using HRMS.Application.Interfaces;

namespace HRMS.API.Controllers;

[ApiController]
[Route("api/v1/leave-balance-sync")]
[Authorize]
public class LeaveBalanceSyncController : BaseController
{
    private readonly ILeaveBalanceSyncService _leaveBalanceSyncService;
    private readonly ILogger<LeaveBalanceSyncController> _logger;

    public LeaveBalanceSyncController(
        ILeaveBalanceSyncService leaveBalanceSyncService,
        ILogger<LeaveBalanceSyncController> logger)
    {
        _leaveBalanceSyncService = leaveBalanceSyncService;
        _logger = logger;
    }

    /// <summary>
    /// Sync leave balance for current employee
    /// </summary>
    /// <returns>Sync result</returns>
    [HttpPost("sync-my-balance")]
    public async Task<ActionResult<ApiResponse>> SyncMyLeaveBalance()
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("Syncing leave balance for current user: {UserId}", userId);

        if (!Guid.TryParse(userId, out var userGuid))
        {
            return BadRequest(ApiResponse.ErrorResult("INVALID_USER_ID", "Invalid user ID"));
        }

        var result = await _leaveBalanceSyncService.SyncEmployeeLeaveBalanceAsync(userGuid);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to sync leave balance for user {UserId}. Error: {Error}", 
                userId, result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Sync leave balance for a specific employee (Admin only)
    /// </summary>
    /// <param name="employeeId">Employee ID</param>
    /// <returns>Sync result</returns>
    [HttpPost("sync-employee/{employeeId}")]
    [Authorize(Roles = "SuperAdmin,OrgAdmin")]
    public async Task<ActionResult<ApiResponse>> SyncEmployeeLeaveBalance(Guid employeeId)
    {
        var currentUserId = GetCurrentUserId();
        _logger.LogInformation("Admin {AdminId} syncing leave balance for employee: {EmployeeId}", 
            currentUserId, employeeId);

        var result = await _leaveBalanceSyncService.SyncEmployeeLeaveBalanceAsync(employeeId);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to sync leave balance for employee {EmployeeId}. Error: {Error}", 
                employeeId, result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Sync leave balances for all employees in current organization (OrgAdmin only)
    /// </summary>
    /// <returns>Sync result</returns>
    [HttpPost("sync-organization")]
    [Authorize(Roles = "SuperAdmin,OrgAdmin")]
    public async Task<ActionResult<ApiResponse>> SyncOrganizationLeaveBalances()
    {
        var currentUserId = GetCurrentUserId();
        var organizationId = GetCurrentOrganizationId();
        
        _logger.LogInformation("Admin {AdminId} syncing leave balances for organization: {OrganizationId}", 
            currentUserId, organizationId);

        if (!Guid.TryParse(organizationId, out var orgGuid))
        {
            return BadRequest(ApiResponse.ErrorResult("INVALID_ORGANIZATION_ID", "Invalid organization ID"));
        }

        var result = await _leaveBalanceSyncService.SyncOrganizationLeaveBalancesAsync(orgGuid);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to sync organization leave balances for org {OrganizationId}. Error: {Error}", 
                organizationId, result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Sync leave balances for all employees in the system (SuperAdmin only)
    /// </summary>
    /// <returns>Sync result</returns>
    [HttpPost("sync-all")]
    [Authorize(Roles = "SuperAdmin")]
    public async Task<ActionResult<ApiResponse>> SyncAllLeaveBalances()
    {
        var currentUserId = GetCurrentUserId();
        _logger.LogInformation("SuperAdmin {AdminId} syncing all leave balances in the system", currentUserId);

        var result = await _leaveBalanceSyncService.SyncAllLeaveBalancesAsync();

        if (!result.Success)
        {
            _logger.LogWarning("Failed to sync all leave balances. Error: {Error}", result.Error?.Message);
            return StatusCode(500, result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Get sync status information
    /// </summary>
    /// <returns>Sync status</returns>
    [HttpGet("status")]
    public async Task<ActionResult<ApiResponse<object>>> GetSyncStatus()
    {
        var currentUserId = GetCurrentUserId();
        var userRole = GetCurrentUserRole();
        
        _logger.LogInformation("Getting sync status for user {UserId} with role {Role}", currentUserId, userRole);

        try
        {
            var status = new
            {
                Message = "Leave balance sync service is active",
                LastSyncTime = DateTime.UtcNow,
                UserRole = userRole,
                AvailableActions = userRole?.ToLower() switch
                {
                    "superadmin" => new[] { "sync-my-balance", "sync-employee", "sync-organization", "sync-all" },
                    "orgadmin" => new[] { "sync-my-balance", "sync-employee", "sync-organization" },
                    "employee" => new[] { "sync-my-balance" },
                    _ => new[] { "sync-my-balance" }
                }
            };

            return Ok(ApiResponse<object>.SuccessResult(status));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting sync status for user {UserId}", currentUserId);
            return StatusCode(500, ApiResponse<object>.ErrorResult("INTERNAL_SERVER_ERROR", 
                "An error occurred while getting sync status"));
        }
    }
}
