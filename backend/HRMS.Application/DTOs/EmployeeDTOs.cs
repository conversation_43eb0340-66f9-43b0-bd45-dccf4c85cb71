namespace HRMS.Application.DTOs;

public class EmployeeDashboardDto
{
    public EmployeeMetricsDto Metrics { get; set; } = new();
    public List<EmployeeActivityDto> RecentActivities { get; set; } = new();
    public EmployeeProfileSummaryDto ProfileSummary { get; set; } = new();
    public List<UpcomingTaskDto> UpcomingTasks { get; set; } = new();
    public EmployeeAttendanceSummaryDto AttendanceSummary { get; set; } = new();
}

public class EmployeeMetricsDto
{
    public int TotalLeaves { get; set; }
    public int UsedLeaves { get; set; }
    public int RemainingLeaves { get; set; }
    public int PendingLeaveRequests { get; set; }
    public decimal TodayHours { get; set; }
    public decimal WeekHours { get; set; }
    public decimal MonthHours { get; set; }
    public int ActiveTasks { get; set; }
    public int CompletedTasks { get; set; }
    public decimal PerformanceScore { get; set; }
    public DateTime? NextReviewDate { get; set; }
}

public class EmployeeActivityDto
{
    public Guid Id { get; set; }
    public string Type { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string Status { get; set; } = string.Empty;
}

public class EmployeeProfileSummaryDto
{
    public string Name { get; set; } = string.Empty;
    public string JobTitle { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string EmployeeId { get; set; } = string.Empty;
    public DateTime JoinDate { get; set; }
    public string Manager { get; set; } = string.Empty;
    public string WorkLocation { get; set; } = string.Empty;
}

public class UpcomingTaskDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime DueDate { get; set; }
    public string Priority { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
}

public class EmployeeAttendanceSummaryDto
{
    public bool IsCheckedIn { get; set; }
    public DateTime? CheckInTime { get; set; }
    public DateTime? CheckOutTime { get; set; }
    public decimal TodayWorkHours { get; set; }
    public decimal WeekWorkHours { get; set; }
    public decimal MonthWorkHours { get; set; }
    public decimal AttendanceRate { get; set; }
    public int PresentDays { get; set; }
    public int AbsentDays { get; set; }
    public int LateDays { get; set; }
}

public class GetEmployeeDashboardRequestDto
{
    public Guid EmployeeId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

public class EmployeeLeaveBalanceDto
{
    public string LeaveType { get; set; } = string.Empty;
    public int TotalDays { get; set; }
    public int UsedDays { get; set; }
    public int RemainingDays { get; set; }
    public int PendingDays { get; set; }
}

public class EmployeeTaskSummaryDto
{
    public int TotalTasks { get; set; }
    public int CompletedTasks { get; set; }
    public int InProgressTasks { get; set; }
    public int OverdueTasks { get; set; }
    public decimal CompletionRate { get; set; }
}

public class EmployeePerformanceDto
{
    public decimal CurrentRating { get; set; }
    public decimal PreviousRating { get; set; }
    public string Trend { get; set; } = string.Empty;
    public DateTime? LastReviewDate { get; set; }
    public DateTime? NextReviewDate { get; set; }
    public List<PerformanceGoalDto> Goals { get; set; } = new();
}


