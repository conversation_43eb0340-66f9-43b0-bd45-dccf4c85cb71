using Microsoft.Extensions.Logging;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;
using HRMS.Core.Enums;
using HRMS.Core.Interfaces;

namespace HRMS.Application.Services;

public class EmployeeService : IEmployeeService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMasterDatabaseService _masterDatabaseService;
    private readonly IDynamicLeaveBalanceService _dynamicLeaveBalanceService;
    private readonly ILeaveBalanceSyncService _leaveBalanceSyncService;
    private readonly ILogger<EmployeeService> _logger;

    public EmployeeService(
        IUnitOfWork unitOfWork,
        IMasterDatabaseService masterDatabaseService,
        IDynamicLeaveBalanceService dynamicLeaveBalanceService,
        ILeaveBalanceSyncService leaveBalanceSyncService,
        ILogger<EmployeeService> logger)
    {
        _unitOfWork = unitOfWork;
        _masterDatabaseService = masterDatabaseService;
        _dynamicLeaveBalanceService = dynamicLeaveBalanceService;
        _leaveBalanceSyncService = leaveBalanceSyncService;
        _logger = logger;
    }

    public async Task<ApiResponse<EmployeeDashboardDto>> GetDashboardAsync(Guid employeeId)
    {
        try
        {
            // Use master database service to get user data
            var user = await _masterDatabaseService.GetUserByIdAsync(employeeId);
            if (user == null)
            {
                return ApiResponse<EmployeeDashboardDto>.ErrorResult("USER_NOT_FOUND", "Employee not found");
            }

            // Get all dashboard data sequentially to avoid DbContext concurrency issues
            var metrics = await GetEmployeeMetricsAsync(employeeId);
            var activities = await GetRecentActivitiesAsync(employeeId, 5);
            var profile = await GetProfileSummaryAsync(employeeId);
            var tasks = await GetUpcomingTasksAsync(employeeId, 5);
            var attendance = await GetAttendanceSummaryAsync(employeeId);

            var dashboard = new EmployeeDashboardDto
            {
                Metrics = metrics.Data ?? new EmployeeMetricsDto(),
                RecentActivities = activities.Data ?? new List<EmployeeActivityDto>(),
                ProfileSummary = profile.Data ?? new EmployeeProfileSummaryDto(),
                UpcomingTasks = tasks.Data ?? new List<UpcomingTaskDto>(),
                AttendanceSummary = attendance.Data ?? new EmployeeAttendanceSummaryDto()
            };

            return ApiResponse<EmployeeDashboardDto>.SuccessResult(dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employee dashboard for user {EmployeeId}", employeeId);
            return ApiResponse<EmployeeDashboardDto>.ErrorResult("INTERNAL_SERVER_ERROR", 
                "An error occurred while getting employee dashboard");
        }
    }

    public async Task<ApiResponse<EmployeeProfileSummaryDto>> GetProfileSummaryAsync(Guid employeeId)
    {
        try
        {
            var user = await _masterDatabaseService.GetUserByIdAsync(employeeId);
            if (user == null)
            {
                return ApiResponse<EmployeeProfileSummaryDto>.ErrorResult("USER_NOT_FOUND", "Employee not found");
            }

            var profile = new EmployeeProfileSummaryDto
            {
                Name = user.Name,
                JobTitle = user.EmployeeDetail?.JobTitle ?? "Not specified",
                Department = user.EmployeeDetail?.Department ?? "Not specified",
                EmployeeId = user.EmployeeDetail?.EmployeeId ?? "Not assigned",
                JoinDate = user.EmployeeDetail?.JoinDate ?? user.CreatedAt,
                Manager = user.EmployeeDetail?.Manager?.Name ?? "Not assigned",
                WorkLocation = user.EmployeeDetail?.WorkLocation ?? "Not specified"
            };

            return ApiResponse<EmployeeProfileSummaryDto>.SuccessResult(profile);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employee profile summary for user {EmployeeId}", employeeId);
            return ApiResponse<EmployeeProfileSummaryDto>.ErrorResult("INTERNAL_SERVER_ERROR", 
                "An error occurred while getting employee profile");
        }
    }

    public async Task<ApiResponse<List<EmployeeLeaveBalanceDto>>> GetLeaveBalancesAsync(Guid employeeId)
    {
        try
        {
            // Use dynamic leave balance calculation
            var dynamicBalanceResult = await _dynamicLeaveBalanceService.CalculateLeaveBalanceAsync(employeeId);

            if (!dynamicBalanceResult.Success || dynamicBalanceResult.Data == null)
            {
                _logger.LogWarning("Failed to get dynamic leave balance for employee {EmployeeId}, returning empty list", employeeId);
                return ApiResponse<List<EmployeeLeaveBalanceDto>>.SuccessResult(new List<EmployeeLeaveBalanceDto>());
            }

            var dynamicBalance = dynamicBalanceResult.Data;

            // Convert dynamic balance to EmployeeLeaveBalanceDto format for backward compatibility
            var result = new List<EmployeeLeaveBalanceDto>
            {
                new EmployeeLeaveBalanceDto
                {
                    LeaveType = "Dynamic Leave Balance",
                    TotalDays = dynamicBalance.TotalAllocated,
                    UsedDays = dynamicBalance.UsedLeaves,
                    RemainingDays = dynamicBalance.RemainingLeaves,
                    PendingDays = dynamicBalance.PendingLeaves
                }
            };

            return ApiResponse<List<EmployeeLeaveBalanceDto>>.SuccessResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting leave balances for user {EmployeeId}", employeeId);
            return ApiResponse<List<EmployeeLeaveBalanceDto>>.ErrorResult("INTERNAL_SERVER_ERROR",
                "An error occurred while getting leave balances");
        }
    }

    public async Task<ApiResponse<EmployeeTaskSummaryDto>> GetTaskSummaryAsync(Guid employeeId)
    {
        try
        {
            var taskRepo = _unitOfWork.Tasks;
            var tasks = await taskRepo.GetAllAsync();
            var userTasks = tasks.Where(t => t.AssignedToId == employeeId).ToList();

            var totalTasks = userTasks.Count;
            var completedTasks = userTasks.Count(t => t.Status == HRMS.Core.Enums.TaskStatus.Completed);
            var inProgressTasks = userTasks.Count(t => t.Status == HRMS.Core.Enums.TaskStatus.InProgress);
            var overdueTasks = userTasks.Count(t =>
                t.DueDate.HasValue &&
                t.DueDate < DateTime.UtcNow &&
                t.Status != HRMS.Core.Enums.TaskStatus.Completed);

            var completionRate = totalTasks > 0 ? Math.Round((decimal)completedTasks / totalTasks * 100, 1) : 0m;

            var summary = new EmployeeTaskSummaryDto
            {
                TotalTasks = totalTasks,
                CompletedTasks = completedTasks,
                InProgressTasks = inProgressTasks,
                OverdueTasks = overdueTasks,
                CompletionRate = completionRate
            };

            return ApiResponse<EmployeeTaskSummaryDto>.SuccessResult(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting task summary for user {EmployeeId}", employeeId);
            return ApiResponse<EmployeeTaskSummaryDto>.ErrorResult("INTERNAL_SERVER_ERROR",
                "An error occurred while getting task summary");
        }
    }

    public async Task<ApiResponse<EmployeeAttendanceSummaryDto>> GetAttendanceSummaryAsync(Guid employeeId)
    {
        try
        {
            var attendanceRepo = _unitOfWork.Attendance;
            var records = await attendanceRepo.GetAllAsync();
            var userRecords = records.Where(ar => ar.UserId == employeeId).ToList();

            var today = DateTime.UtcNow.Date;
            var todayRecord = userRecords.FirstOrDefault(ar => ar.Date.Date == today);

            var startOfWeek = today.AddDays(-(int)today.DayOfWeek);
            var weekRecords = userRecords.Where(ar => ar.Date >= startOfWeek && ar.Date <= today).ToList();

            var startOfMonth = new DateTime(today.Year, today.Month, 1);
            var monthRecords = userRecords.Where(ar => ar.Date >= startOfMonth && ar.Date <= today).ToList();

            var summary = new EmployeeAttendanceSummaryDto
            {
                IsCheckedIn = todayRecord?.CheckInTime != null && todayRecord.CheckOutTime == null,
                CheckInTime = todayRecord?.CheckInTime,
                CheckOutTime = todayRecord?.CheckOutTime,
                TodayWorkHours = todayRecord?.TotalHours ?? 0m,
                WeekWorkHours = weekRecords.Sum(ar => ar.TotalHours ?? 0m),
                MonthWorkHours = monthRecords.Sum(ar => ar.TotalHours ?? 0m),
                AttendanceRate = monthRecords.Any() ?
                    Math.Round((decimal)monthRecords.Count(ar => ar.Status == HRMS.Core.Enums.AttendanceStatus.Present) / monthRecords.Count * 100, 1) : 0m,
                PresentDays = monthRecords.Count(ar => ar.Status == HRMS.Core.Enums.AttendanceStatus.Present),
                AbsentDays = monthRecords.Count(ar => ar.Status == HRMS.Core.Enums.AttendanceStatus.Absent),
                LateDays = monthRecords.Count(ar => ar.Status == HRMS.Core.Enums.AttendanceStatus.Late)
            };

            return ApiResponse<EmployeeAttendanceSummaryDto>.SuccessResult(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting attendance summary for user {EmployeeId}", employeeId);
            return ApiResponse<EmployeeAttendanceSummaryDto>.ErrorResult("INTERNAL_SERVER_ERROR",
                "An error occurred while getting attendance summary");
        }
    }

    public async Task<ApiResponse<EmployeePerformanceDto>> GetPerformanceDataAsync(Guid employeeId)
    {
        try
        {
            var performanceRepo = _unitOfWork.Performance;
            var reviews = await performanceRepo.GetAllAsync();
            var userReviews = reviews.Where(pr => pr.EmployeeId == employeeId).OrderByDescending(pr => pr.ReviewDate).ToList();

            var currentReview = userReviews.FirstOrDefault();
            var previousReview = userReviews.Skip(1).FirstOrDefault();

            var currentRating = currentReview?.OverallRating ?? 0m;
            var previousRating = previousReview?.OverallRating ?? 0m;

            string trend = "stable";
            if (currentRating > previousRating) trend = "improving";
            else if (currentRating < previousRating) trend = "declining";

            var nextReview = reviews.FirstOrDefault(pr => pr.EmployeeId == employeeId &&
                                                         pr.Status == HRMS.Core.Enums.ReviewStatus.Scheduled &&
                                                         pr.ReviewDate > DateTime.UtcNow);

            var performance = new EmployeePerformanceDto
            {
                CurrentRating = currentRating,
                PreviousRating = previousRating,
                Trend = trend,
                LastReviewDate = currentReview?.ReviewDate,
                NextReviewDate = nextReview?.ReviewDate,
                Goals = new List<PerformanceGoalDto>() // Goals would come from a separate Goals entity if implemented
            };

            return ApiResponse<EmployeePerformanceDto>.SuccessResult(performance);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance data for user {EmployeeId}", employeeId);
            return ApiResponse<EmployeePerformanceDto>.ErrorResult("INTERNAL_SERVER_ERROR",
                "An error occurred while getting performance data");
        }
    }

    public async Task<ApiResponse<List<EmployeeActivityDto>>> GetRecentActivitiesAsync(Guid employeeId, int limit = 10)
    {
        try
        {
            var activities = new List<EmployeeActivityDto>();

            // Get recent tasks
            var taskRepo = _unitOfWork.Tasks;
            var tasks = await taskRepo.GetAllAsync();
            var userTasks = tasks.Where(t => t.AssignedToId == employeeId && t.UpdatedAt >= DateTime.UtcNow.AddDays(-7))
                                .OrderByDescending(t => t.UpdatedAt)
                                .Take(3);

            foreach (var task in userTasks)
            {
                activities.Add(new EmployeeActivityDto
                {
                    Id = Guid.NewGuid(),
                    Type = "task",
                    Message = $"Task '{task.Title}' {task.Status.ToString().ToLower()}",
                    Timestamp = task.UpdatedAt,
                    Status = task.Status.ToString().ToLower()
                });
            }

            // Get recent leave requests
            var leaveRepo = _unitOfWork.Leaves;
            var leaves = await leaveRepo.GetAllAsync();
            var userLeaves = leaves.Where(l => l.UserId == employeeId && l.CreatedAt >= DateTime.UtcNow.AddDays(-7))
                                  .OrderByDescending(l => l.CreatedAt)
                                  .Take(2);

            foreach (var leave in userLeaves)
            {
                activities.Add(new EmployeeActivityDto
                {
                    Id = Guid.NewGuid(),
                    Type = "leave",
                    Message = $"{leave.LeaveType?.Name ?? "Leave"} request {leave.Status.ToString().ToLower()}",
                    Timestamp = leave.CreatedAt,
                    Status = leave.Status.ToString().ToLower()
                });
            }

            // Get recent attendance
            var attendanceRepo = _unitOfWork.Attendance;
            var attendance = await attendanceRepo.GetAllAsync();
            var userAttendance = attendance.Where(a => a.UserId == employeeId && a.Date >= DateTime.UtcNow.AddDays(-3))
                                          .OrderByDescending(a => a.Date)
                                          .Take(2);

            foreach (var att in userAttendance)
            {
                activities.Add(new EmployeeActivityDto
                {
                    Id = Guid.NewGuid(),
                    Type = "attendance",
                    Message = $"Check-in recorded at {att.CheckInTime?.ToString("HH:mm") ?? "N/A"}",
                    Timestamp = att.Date,
                    Status = "info"
                });
            }

            // If no activities found, add a default one
            if (!activities.Any())
            {
                activities.Add(new EmployeeActivityDto
                {
                    Id = Guid.NewGuid(),
                    Type = "system",
                    Message = "Dashboard accessed",
                    Timestamp = DateTime.UtcNow,
                    Status = "info"
                });
            }

            return ApiResponse<List<EmployeeActivityDto>>.SuccessResult(activities.Take(limit).ToList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting recent activities for user {EmployeeId}", employeeId);
            return ApiResponse<List<EmployeeActivityDto>>.ErrorResult("INTERNAL_SERVER_ERROR",
                "An error occurred while getting recent activities");
        }
    }

    public async Task<ApiResponse<List<UpcomingTaskDto>>> GetUpcomingTasksAsync(Guid employeeId, int limit = 5)
    {
        try
        {
            var taskRepo = _unitOfWork.Tasks;
            var tasks = await taskRepo.GetAllAsync();

            var upcomingTasks = tasks
                .Where(t => t.AssignedToId == employeeId &&
                           t.Status != HRMS.Core.Enums.TaskStatus.Completed &&
                           t.DueDate >= DateTime.UtcNow)
                .OrderBy(t => t.DueDate)
                .Take(limit)
                .Select(t => new UpcomingTaskDto
                {
                    Id = t.Id,
                    Title = t.Title,
                    Description = t.Description,
                    DueDate = t.DueDate ?? DateTime.UtcNow.AddDays(7), // Default to 7 days from now if no due date
                    Priority = t.Priority.ToString(),
                    Status = t.Status.ToString()
                })
                .ToList();

            // If no upcoming tasks, return empty list instead of mock data

            return ApiResponse<List<UpcomingTaskDto>>.SuccessResult(upcomingTasks.Take(limit).ToList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting upcoming tasks for user {EmployeeId}", employeeId);
            return ApiResponse<List<UpcomingTaskDto>>.ErrorResult("INTERNAL_SERVER_ERROR",
                "An error occurred while getting upcoming tasks");
        }
    }

    private async Task<ApiResponse<EmployeeMetricsDto>> GetEmployeeMetricsAsync(Guid employeeId)
    {
        try
        {
            // Use dynamic leave balance calculation instead of static leave balances
            var dynamicLeaveBalanceResult = await _dynamicLeaveBalanceService.CalculateLeaveBalanceAsync(employeeId);
            var taskSummaryResult = await GetTaskSummaryAsync(employeeId);
            var attendanceSummaryResult = await GetAttendanceSummaryAsync(employeeId);
            var performanceResult = await GetPerformanceDataAsync(employeeId);

            var taskSummary = taskSummaryResult.Data ?? new EmployeeTaskSummaryDto();
            var attendanceSummary = attendanceSummaryResult.Data ?? new EmployeeAttendanceSummaryDto();
            var performance = performanceResult.Data ?? new EmployeePerformanceDto();

            // Use dynamic leave balance data or fallback to defaults
            var totalLeaves = 0;
            var usedLeaves = 0;
            var remainingLeaves = 0;
            var pendingLeaves = 0;

            if (dynamicLeaveBalanceResult.Success && dynamicLeaveBalanceResult.Data != null)
            {
                var dynamicBalance = dynamicLeaveBalanceResult.Data;
                totalLeaves = dynamicBalance.TotalAllocated;
                usedLeaves = dynamicBalance.UsedLeaves;
                remainingLeaves = dynamicBalance.RemainingLeaves;
                pendingLeaves = dynamicBalance.PendingLeaves;

                // Sync the calculated balance to the database so frontend gets correct data
                var syncResult = await _leaveBalanceSyncService.SyncEmployeeLeaveBalanceAsync(employeeId);
                if (!syncResult.Success)
                {
                    _logger.LogWarning("Failed to sync leave balance to database for employee {EmployeeId}: {Error}",
                        employeeId, syncResult.Error?.Message);
                }

                _logger.LogInformation("Using and synced dynamic leave balance for employee {EmployeeId}: Total={Total}, Used={Used}, Remaining={Remaining}",
                    employeeId, totalLeaves, usedLeaves, remainingLeaves);
            }
            else
            {
                _logger.LogWarning("Failed to get dynamic leave balance for employee {EmployeeId}, using defaults", employeeId);
            }

            var metrics = new EmployeeMetricsDto
            {
                TotalLeaves = totalLeaves,
                UsedLeaves = usedLeaves,
                RemainingLeaves = remainingLeaves,
                PendingLeaveRequests = pendingLeaves,
                TodayHours = attendanceSummary.TodayWorkHours,
                WeekHours = attendanceSummary.WeekWorkHours,
                MonthHours = attendanceSummary.MonthWorkHours,
                ActiveTasks = taskSummary.InProgressTasks,
                CompletedTasks = taskSummary.CompletedTasks,
                PerformanceScore = performance.CurrentRating,
                NextReviewDate = performance.NextReviewDate
            };

            return ApiResponse<EmployeeMetricsDto>.SuccessResult(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employee metrics for user {EmployeeId}", employeeId);
            return ApiResponse<EmployeeMetricsDto>.ErrorResult("INTERNAL_SERVER_ERROR", 
                "An error occurred while getting employee metrics");
        }
    }


}
