using Microsoft.Extensions.Logging;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;
using HRMS.Core.Entities;
using HRMS.Core.Enums;
using HRMS.Core.Interfaces;
using HRMS.Infrastructure.Interfaces;

namespace HRMS.Application.Services;

public class AttendanceService : IAttendanceService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ITenantService _tenantService;
    private readonly ITimeZoneService _timeZoneService;
    private readonly ILogger<AttendanceService> _logger;

    public AttendanceService(
        IUnitOfWork unitOfWork,
        ITenantService tenantService,
        ITimeZoneService timeZoneService,
        ILogger<AttendanceService> logger)
    {
        _unitOfWork = unitOfWork;
        _tenantService = tenantService;
        _timeZoneService = timeZoneService;
        _logger = logger;
    }

    public async Task<ApiResponse<AttendanceResponseDto>> CheckInAsync(string userId, CheckInRequestDto request)
    {
        try
        {
            var userGuid = Guid.Parse(userId);

            // Use IST for all date/time operations
            var currentIstTime = _timeZoneService.GetCurrentIstTime();
            var todayIst = _timeZoneService.GetIstStartOfDay(currentIstTime);

            // Convert the provided timestamp to IST if it's in UTC
            var checkInTimeIst = request.Timestamp.Kind == DateTimeKind.Utc
                ? _timeZoneService.ConvertUtcToIst(request.Timestamp)
                : request.Timestamp;

            _logger.LogInformation("Check-in request for user {UserId} at IST time: {CheckInTime}",
                userId, _timeZoneService.FormatIstDateTime(checkInTimeIst));

            // Check if user already checked in today (IST date)
            var existingRecord = await _unitOfWork.Attendance.GetByUserAndDateAsync(userGuid, todayIst);
            if (existingRecord != null && existingRecord.CheckInTime.HasValue)
            {
                return ApiResponse<AttendanceResponseDto>.ErrorResult("ALREADY_CHECKED_IN",
                    $"You have already checked in today at {_timeZoneService.FormatIstTime(_timeZoneService.ConvertUtcToIst(existingRecord.CheckInTime.Value))} IST");
            }

            AttendanceRecord attendanceRecord;

            // Determine status based on IST time
            var lateThreshold = new TimeSpan(9, 30, 0); // 9:30 AM IST
            var isLate = _timeZoneService.IsLateCheckIn(checkInTimeIst, lateThreshold);
            var status = isLate ? AttendanceStatus.Late : AttendanceStatus.Present;

            if (existingRecord != null)
            {
                // Update existing record
                existingRecord.CheckInTime = _timeZoneService.ConvertIstToUtc(checkInTimeIst); // Store as UTC in DB
                existingRecord.Location = request.Location;
                existingRecord.Notes = request.Notes;
                existingRecord.Status = status;

                await _unitOfWork.Attendance.UpdateAsync(existingRecord);
                attendanceRecord = existingRecord;
            }
            else
            {
                // Create new record
                attendanceRecord = new AttendanceRecord
                {
                    UserId = userGuid,
                    Date = todayIst, // Store IST date
                    CheckInTime = _timeZoneService.ConvertIstToUtc(checkInTimeIst), // Store as UTC in DB
                    Location = request.Location,
                    Notes = request.Notes,
                    Status = status
                };

                await _unitOfWork.Attendance.AddAsync(attendanceRecord);
            }

            await _unitOfWork.SaveChangesAsync();

            var response = new AttendanceResponseDto
            {
                Id = attendanceRecord.Id,
                Date = attendanceRecord.Date,
                CheckInTime = attendanceRecord.CheckInTime, // Will be converted to IST in response
                CheckOutTime = attendanceRecord.CheckOutTime,
                TotalHours = attendanceRecord.TotalHours,
                Status = attendanceRecord.Status.ToString().ToLowerInvariant(),
                Location = attendanceRecord.Location,
                Notes = attendanceRecord.Notes,
                Message = $"Check-in successful at {_timeZoneService.FormatIstTime(checkInTimeIst)} IST" +
                         (isLate ? " (Late arrival)" : "")
            };

            return ApiResponse<AttendanceResponseDto>.SuccessResult(response, "Check-in successful");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during check-in for user {UserId}", userId);
            return ApiResponse<AttendanceResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred during check-in");
        }
    }

    public async Task<ApiResponse<AttendanceResponseDto>> CheckOutAsync(string userId, CheckOutRequestDto request)
    {
        try
        {
            var userGuid = Guid.Parse(userId);

            // Use IST for all date/time operations
            var currentIstTime = _timeZoneService.GetCurrentIstTime();
            var todayIst = _timeZoneService.GetIstStartOfDay(currentIstTime);

            // Convert the provided timestamp to IST if it's in UTC
            var checkOutTimeIst = request.Timestamp.Kind == DateTimeKind.Utc
                ? _timeZoneService.ConvertUtcToIst(request.Timestamp)
                : request.Timestamp;

            _logger.LogInformation("Check-out request for user {UserId} at IST time: {CheckOutTime}",
                userId, _timeZoneService.FormatIstDateTime(checkOutTimeIst));

            // Find today's attendance record (IST date)
            var attendanceRecord = await _unitOfWork.Attendance.GetByUserAndDateAsync(userGuid, todayIst);
            if (attendanceRecord == null || !attendanceRecord.CheckInTime.HasValue)
            {
                return ApiResponse<AttendanceResponseDto>.ErrorResult("NOT_CHECKED_IN", "You must check in before checking out");
            }

            if (attendanceRecord.CheckOutTime.HasValue)
            {
                var existingCheckOutIst = _timeZoneService.ConvertUtcToIst(attendanceRecord.CheckOutTime.Value);
                return ApiResponse<AttendanceResponseDto>.ErrorResult("ALREADY_CHECKED_OUT",
                    $"You have already checked out today at {_timeZoneService.FormatIstTime(existingCheckOutIst)} IST");
            }

            // Update record with check-out time (store as UTC in DB)
            attendanceRecord.CheckOutTime = _timeZoneService.ConvertIstToUtc(checkOutTimeIst);
            attendanceRecord.Notes = string.IsNullOrEmpty(attendanceRecord.Notes)
                ? request.Notes
                : $"{attendanceRecord.Notes}; {request.Notes}";

            // Calculate total hours using IST times
            if (attendanceRecord.CheckInTime.HasValue)
            {
                var checkInIst = _timeZoneService.ConvertUtcToIst(attendanceRecord.CheckInTime.Value);
                attendanceRecord.TotalHours = _timeZoneService.CalculateWorkingHours(checkInIst, checkOutTimeIst);
            }

            await _unitOfWork.Attendance.UpdateAsync(attendanceRecord);
            await _unitOfWork.SaveChangesAsync();

            var response = new AttendanceResponseDto
            {
                Id = attendanceRecord.Id,
                Date = attendanceRecord.Date,
                CheckInTime = attendanceRecord.CheckInTime, // Will be converted to IST in response
                CheckOutTime = attendanceRecord.CheckOutTime, // Will be converted to IST in response
                TotalHours = attendanceRecord.TotalHours,
                Status = attendanceRecord.Status.ToString().ToLowerInvariant(),
                Location = attendanceRecord.Location,
                Notes = attendanceRecord.Notes,
                Message = $"Check-out successful at {_timeZoneService.FormatIstTime(checkOutTimeIst)} IST. " +
                         $"Total working hours: {attendanceRecord.TotalHours:F2} hours"
            };

            return ApiResponse<AttendanceResponseDto>.SuccessResult(response, "Check-out successful");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during check-out for user {UserId}", userId);
            return ApiResponse<AttendanceResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred during check-out");
        }
    }

    public async Task<ApiResponse<AttendanceRecordsResponseDto>> GetAttendanceRecordsAsync(GetAttendanceRequestDto request)
    {
        try
        {
            // Use IST for date range calculations
            var currentIstTime = _timeZoneService.GetCurrentIstTime();
            var startDate = request.StartDate.HasValue
                ? _timeZoneService.GetIstStartOfDay(request.StartDate.Value)
                : _timeZoneService.GetIstStartOfDay(currentIstTime.AddDays(-30));
            var endDate = request.EndDate.HasValue
                ? _timeZoneService.GetIstEndOfDay(request.EndDate.Value)
                : _timeZoneService.GetIstEndOfDay(currentIstTime);

            IEnumerable<AttendanceRecord> records;

            if (request.UserId.HasValue)
            {
                records = await _unitOfWork.Attendance.GetByUserAndDateRangeAsync(request.UserId.Value, startDate, endDate);
            }
            else
            {
                // Get all records for the date range (admin view)
                records = await _unitOfWork.Attendance.FindAsync(a => 
                    a.Date >= startDate && a.Date <= endDate);
            }

            // Apply status filter if provided
            if (!string.IsNullOrEmpty(request.Status))
            {
                if (Enum.TryParse<AttendanceStatus>(request.Status, true, out var statusEnum))
                {
                    records = records.Where(r => r.Status == statusEnum);
                }
            }

            var recordDtos = records.Select(r => new AttendanceRecordDto
            {
                Id = r.Id,
                Date = r.Date,
                CheckInTime = r.CheckInTime,
                CheckOutTime = r.CheckOutTime,
                TotalHours = r.TotalHours,
                Status = r.Status.ToString().ToLowerInvariant(),
                Location = r.Location,
                Notes = r.Notes
            }).ToList();

            // Calculate summary
            var summary = new AttendanceSummaryDto
            {
                TotalDays = recordDtos.Count,
                PresentDays = recordDtos.Count(r => r.Status == "present"),
                AbsentDays = recordDtos.Count(r => r.Status == "absent"),
                LateDays = recordDtos.Count(r => r.Status == "late"),
                TotalHours = recordDtos.Sum(r => r.TotalHours ?? 0)
            };

            var response = new AttendanceRecordsResponseDto
            {
                Records = recordDtos,
                Summary = summary
            };

            return ApiResponse<AttendanceRecordsResponseDto>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting attendance records");
            return ApiResponse<AttendanceRecordsResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while getting attendance records");
        }
    }

    public async Task<ApiResponse<OrganizationAttendanceResponseDto>> GetOrganizationAttendanceAsync(GetOrganizationAttendanceRequestDto request)
    {
        try
        {
            var tenant = _tenantService.GetCurrentTenant();
            if (string.IsNullOrEmpty(tenant.OrganizationId))
            {
                return ApiResponse<OrganizationAttendanceResponseDto>.ErrorResult("INVALID_TENANT", "No organization context found");
            }

            // Use IST for all date operations
            var currentIstTime = _timeZoneService.GetCurrentIstTime();
            var todayIst = request.Date.HasValue
                ? _timeZoneService.GetIstStartOfDay(request.Date.Value)
                : _timeZoneService.GetIstStartOfDay(currentIstTime);
            var startDate = request.StartDate.HasValue
                ? _timeZoneService.GetIstStartOfDay(request.StartDate.Value)
                : todayIst;
            var endDate = request.EndDate.HasValue
                ? _timeZoneService.GetIstEndOfDay(request.EndDate.Value)
                : _timeZoneService.GetIstEndOfDay(todayIst);

            // Get all employees in the organization
            var employees = await _unitOfWork.Users.FindAsync(u =>
                u.OrganizationId == Guid.Parse(tenant.OrganizationId) &&
                (u.Role == UserRole.OrgAdmin || u.Role == UserRole.Employee) &&
                u.IsActive && !u.IsDeleted);

            // Get attendance records for the date range
            var attendanceRecords = await _unitOfWork.Attendance.FindAsync(a =>
                a.Date >= startDate && a.Date <= endDate);

            var attendanceMap = attendanceRecords.GroupBy(a => a.UserId)
                .ToDictionary(g => g.Key, g => g.ToList());

            var employeeAttendanceList = new List<EmployeeAttendanceDto>();

            foreach (var employee in employees)
            {
                var employeeAttendance = attendanceMap.ContainsKey(employee.Id)
                    ? attendanceMap[employee.Id]
                    : new List<AttendanceRecord>();

                // Get today's attendance record (using IST date)
                var todayRecord = employeeAttendance.FirstOrDefault(a => a.Date.Date == todayIst.Date);

                var status = DetermineAttendanceStatus(todayRecord, todayIst);

                employeeAttendanceList.Add(new EmployeeAttendanceDto
                {
                    UserId = employee.Id,
                    Name = employee.Name,
                    Email = employee.Email,
                    EmployeeId = employee.EmployeeDetail?.EmployeeId ?? "",
                    Department = employee.EmployeeDetail?.Department ?? "",
                    JobTitle = employee.EmployeeDetail?.JobTitle ?? "",
                    Status = status,
                    CheckInTime = todayRecord?.CheckInTime.HasValue == true
                        ? _timeZoneService.ConvertUtcToIst(todayRecord.CheckInTime.Value)
                        : null,
                    CheckOutTime = todayRecord?.CheckOutTime.HasValue == true
                        ? _timeZoneService.ConvertUtcToIst(todayRecord.CheckOutTime.Value)
                        : null,
                    TotalHours = todayRecord?.TotalHours,
                    Location = todayRecord?.Location,
                    Notes = todayRecord?.Notes,
                    AttendanceRecords = employeeAttendance.Select(a => new AttendanceRecordDto
                    {
                        Id = a.Id,
                        Date = a.Date,
                        CheckInTime = a.CheckInTime.HasValue
                            ? _timeZoneService.ConvertUtcToIst(a.CheckInTime.Value)
                            : null,
                        CheckOutTime = a.CheckOutTime.HasValue
                            ? _timeZoneService.ConvertUtcToIst(a.CheckOutTime.Value)
                            : null,
                        TotalHours = a.TotalHours,
                        Status = a.Status.ToString().ToLowerInvariant(),
                        Location = a.Location,
                        Notes = a.Notes
                    }).ToList()
                });
            }

            // Calculate organization-wide statistics
            var stats = new OrganizationAttendanceStatsDto
            {
                TotalEmployees = employeeAttendanceList.Count,
                PresentToday = employeeAttendanceList.Count(e => e.Status == "present"),
                AbsentToday = employeeAttendanceList.Count(e => e.Status == "absent"),
                LateToday = employeeAttendanceList.Count(e => e.Status == "late"),
                OnLeaveToday = employeeAttendanceList.Count(e => e.Status == "on_leave"),
                AttendanceRate = employeeAttendanceList.Count > 0
                    ? Math.Round(((double)employeeAttendanceList.Count(e => e.Status == "present" || e.Status == "late") / employeeAttendanceList.Count) * 100, 2)
                    : 0
            };

            var response = new OrganizationAttendanceResponseDto
            {
                Date = todayIst, // Return IST date
                Employees = employeeAttendanceList,
                Statistics = stats
            };

            return ApiResponse<OrganizationAttendanceResponseDto>.SuccessResult(response, "Organization attendance retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving organization attendance");
            return ApiResponse<OrganizationAttendanceResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while retrieving organization attendance");
        }
    }

    private string DetermineAttendanceStatus(AttendanceRecord? record, DateTime istDate)
    {
        if (record == null)
        {
            // Check if it's a weekend or holiday (simplified logic using IST date)
            if (istDate.DayOfWeek == DayOfWeek.Saturday || istDate.DayOfWeek == DayOfWeek.Sunday)
            {
                return "weekend";
            }
            return "absent";
        }

        // Map AttendanceStatus enum values to string representations
        switch (record.Status)
        {
            case AttendanceStatus.Present:
                // Check if late based on IST check-in time
                if (record.CheckInTime.HasValue)
                {
                    var lateThreshold = new TimeSpan(9, 30, 0); // 9:30 AM IST
                    var checkInTimeIst = _timeZoneService.ConvertUtcToIst(record.CheckInTime.Value);

                    if (_timeZoneService.IsLateCheckIn(checkInTimeIst, lateThreshold))
                    {
                        return "late";
                    }
                }
                return "present";

            case AttendanceStatus.Absent:
                return "absent";

            case AttendanceStatus.Late:
                return "late";

            case AttendanceStatus.HalfDay:
                return "half_day";

            default:
                return "absent";
        }
    }
}
