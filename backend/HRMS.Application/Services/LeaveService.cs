using Microsoft.Extensions.Logging;
using HRMS.Application.Common;
using HRMS.Application.DTOs;
using HRMS.Application.Interfaces;
using HRMS.Core.Entities;
using HRMS.Core.Enums;
using HRMS.Core.Interfaces;
using HRMS.Infrastructure.Interfaces;

namespace HRMS.Application.Services;

public class LeaveService : ILeaveService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ITenantService _tenantService;
    private readonly ITimeZoneService _timeZoneService;
    private readonly IDynamicLeaveBalanceService _dynamicLeaveBalanceService;
    private readonly ILeaveBalanceSyncService _leaveBalanceSyncService;
    private readonly ILogger<LeaveService> _logger;

    public LeaveService(
        IUnitOfWork unitOfWork,
        ITenantService tenantService,
        ITimeZoneService timeZoneService,
        IDynamicLeaveBalanceService dynamicLeaveBalanceService,
        ILeaveBalanceSyncService leaveBalanceSyncService,
        ILogger<LeaveService> logger)
    {
        _unitOfWork = unitOfWork;
        _tenantService = tenantService;
        _timeZoneService = timeZoneService;
        _dynamicLeaveBalanceService = dynamicLeaveBalanceService;
        _leaveBalanceSyncService = leaveBalanceSyncService;
        _logger = logger;
    }


    public async Task<ApiResponse<LeaveTypesResponseDto>> GetLeaveTypesAsync(string organizationId)
    {
        try
        {
            if (!Guid.TryParse(organizationId, out var orgGuid))
            {
                return ApiResponse<LeaveTypesResponseDto>.ErrorResult("INVALID_ORGANIZATION", "Invalid organization context");
            }

            var types = await _unitOfWork.LeaveTypes.GetByOrganizationAsync(orgGuid);
            var dto = new LeaveTypesResponseDto
            {
                Types = types.Select(t => new LeaveTypeDto
                {
                    Id = t.Id,
                    Name = t.Name,
                    Description = t.Description ?? string.Empty,
                    MaxDaysPerYear = t.MaxDaysPerYear,
                    IsActive = t.IsActive
                }).ToList()
            };

            return ApiResponse<LeaveTypesResponseDto>.SuccessResult(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting leave types for organization {OrganizationId}", organizationId);
            return ApiResponse<LeaveTypesResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while getting leave types");
        }
    }

    public async Task<ApiResponse<LeaveBalanceResponseDto>> GetLeaveBalanceAsync(GetLeaveBalanceRequestDto request)
    {
        try
        {
            var userId = request.UserId ?? throw new ArgumentException("UserId is required");

            // Use dynamic leave balance calculation instead of static LeaveBalance records
            var dynamicBalanceResult = await _dynamicLeaveBalanceService.CalculateLeaveBalanceAsync(userId);

            if (!dynamicBalanceResult.Success || dynamicBalanceResult.Data == null)
            {
                _logger.LogWarning("Failed to calculate dynamic leave balance for user {UserId}: {Error}",
                    userId, dynamicBalanceResult.Error?.Message);
                return ApiResponse<LeaveBalanceResponseDto>.ErrorResult(
                    dynamicBalanceResult.Error?.Code ?? "CALCULATION_ERROR",
                    dynamicBalanceResult.Error?.Message ?? "Failed to calculate leave balance");
            }

            var dynamicBalance = dynamicBalanceResult.Data;

            // Sync the calculated balance to the database so frontend gets correct data
            var syncResult = await _leaveBalanceSyncService.SyncEmployeeLeaveBalanceAsync(userId);
            if (!syncResult.Success)
            {
                _logger.LogWarning("Failed to sync leave balance to database for user {UserId}: {Error}",
                    userId, syncResult.Error?.Message);
                // Continue anyway - we can still return the calculated balance
            }

            var response = new LeaveBalanceResponseDto
            {
                Balances = new List<LeaveBalanceDto>
                {
                    new LeaveBalanceDto
                    {
                        LeaveType = "Dynamic Leave Balance",
                        TotalAllocated = dynamicBalance.TotalAllocated,
                        UsedLeaves = dynamicBalance.UsedLeaves,
                        RemainingLeaves = dynamicBalance.RemainingLeaves,
                        CarriedForward = 0 // Dynamic system doesn't use traditional carryforward
                    }
                }
            };

            _logger.LogInformation("Retrieved and synced dynamic leave balance for user {UserId}: Total={Total}, Used={Used}, Remaining={Remaining}",
                userId, dynamicBalance.TotalAllocated, dynamicBalance.UsedLeaves, dynamicBalance.RemainingLeaves);

            return ApiResponse<LeaveBalanceResponseDto>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting leave balance for user {UserId}", request.UserId);
            return ApiResponse<LeaveBalanceResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while getting leave balance");
        }
    }

    public async Task<ApiResponse<LeaveRequestResponseDto>> ApplyLeaveAsync(string userId, ApplyLeaveRequestDto request)
    {
        try
        {
            var userGuid = Guid.Parse(userId);

            // Use IST for all date operations
            var currentIstTime = _timeZoneService.GetCurrentIstTime();

            // Convert request dates to IST if needed
            var fromDateIst = request.FromDate.Kind == DateTimeKind.Utc
                ? _timeZoneService.ConvertUtcToIst(request.FromDate)
                : request.FromDate;
            var toDateIst = request.ToDate.Kind == DateTimeKind.Utc
                ? _timeZoneService.ConvertUtcToIst(request.ToDate)
                : request.ToDate;

            _logger.LogInformation("Leave application for user {UserId} from {FromDate} to {ToDate} IST",
                userId, _timeZoneService.FormatIstDate(fromDateIst), _timeZoneService.FormatIstDate(toDateIst));

            // Calculate total days
            var totalDays = CalculateLeaveDays(fromDateIst, toDateIst, request.DurationType);

            // Check monthly leave allowance
            var currentYear = fromDateIst.Year;
            var currentMonth = fromDateIst.Month;

            // Ensure monthly allowance exists
            await _unitOfWork.MonthlyLeaveAllowances.CreateOrUpdateAllowanceAsync(userGuid, currentYear, currentMonth);

            // Check if user has available leaves for this month
            var hasAvailableLeave = await _unitOfWork.MonthlyLeaveAllowances.HasAvailableLeaveAsync(userGuid, currentYear, currentMonth);
            var remainingLeaves = await _unitOfWork.MonthlyLeaveAllowances.GetRemainingLeavesAsync(userGuid, currentYear, currentMonth);

            if (!hasAvailableLeave || remainingLeaves < totalDays)
            {
                return ApiResponse<LeaveRequestResponseDto>.ErrorResult("INSUFFICIENT_BALANCE",
                    $"Insufficient leave balance. You have {remainingLeaves} leave(s) remaining for {currentMonth:D2}/{currentYear}");
            }

            // Check for overlapping leave requests
            var existingRequests = await _unitOfWork.Leaves.GetByUserAsync(userGuid);
            var hasOverlap = existingRequests.Any(lr =>
                lr.Status != LeaveStatus.Rejected &&
                lr.Status != LeaveStatus.Cancelled &&
                ((fromDateIst >= lr.FromDate && fromDateIst <= lr.ToDate) ||
                 (toDateIst >= lr.FromDate && toDateIst <= lr.ToDate) ||
                 (fromDateIst <= lr.FromDate && toDateIst >= lr.ToDate)));

            if (hasOverlap)
            {
                return ApiResponse<LeaveRequestResponseDto>.ErrorResult("OVERLAPPING_LEAVE", "Leave request overlaps with existing request");
            }

            // Create leave request (store dates in IST) - no leave type needed for simplified system
            var leaveRequest = new LeaveRequest
            {
                UserId = userGuid,
                LeaveTypeId = null, // No leave type in simplified system
                FromDate = fromDateIst,
                ToDate = toDateIst,
                TotalDays = totalDays,
                DurationType = request.DurationType,
                Reason = request.Reason,
                Status = LeaveStatus.Pending
            };

            await _unitOfWork.Leaves.AddAsync(leaveRequest);
            await _unitOfWork.SaveChangesAsync();

            var response = new LeaveRequestResponseDto
            {
                Id = leaveRequest.Id,
                FromDate = leaveRequest.FromDate,
                ToDate = leaveRequest.ToDate,
                TotalDays = leaveRequest.TotalDays,
                Status = leaveRequest.Status.ToString().ToLowerInvariant(),
                Reason = leaveRequest.Reason,
                CreatedAt = leaveRequest.CreatedAt
            };

            return ApiResponse<LeaveRequestResponseDto>.SuccessResult(response,
                $"Leave request submitted successfully for {_timeZoneService.FormatIstDate(fromDateIst)} to {_timeZoneService.FormatIstDate(toDateIst)} IST");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying leave for user {UserId}", userId);
            return ApiResponse<LeaveRequestResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while applying for leave");
        }
    }

    public async Task<ApiResponse<LeaveRequestsResponseDto>> GetLeaveRequestsAsync(GetLeaveRequestsDto request)
    {
        try
        {
            _logger.LogInformation("GetLeaveRequestsAsync called with UserId: {UserId}", request.UserId);

            IEnumerable<LeaveRequest> leaveRequests;

            if (request.UserId.HasValue)
            {
                _logger.LogInformation("Fetching leave requests for specific user: {UserId}", request.UserId.Value);
                leaveRequests = await _unitOfWork.Leaves.GetByUserAsync(request.UserId.Value);
                _logger.LogInformation("Found {Count} leave requests for user: {UserId}",
                    leaveRequests?.Count() ?? 0, request.UserId.Value);
            }
            else
            {
                _logger.LogInformation("Fetching all leave requests (admin view)");
                // Get all leave requests (admin view)
                leaveRequests = await _unitOfWork.Leaves.GetAllAsync();
                _logger.LogInformation("Found {Count} total leave requests", leaveRequests?.Count() ?? 0);
            }

            // Apply filters
            if (!string.IsNullOrEmpty(request.Status))
            {
                if (Enum.TryParse<LeaveStatus>(request.Status, true, out var statusEnum))
                {
                    leaveRequests = leaveRequests.Where(lr => lr.Status == statusEnum);
                }
            }

            if (request.StartDate.HasValue)
            {
                leaveRequests = leaveRequests.Where(lr => lr.FromDate >= request.StartDate.Value);
            }

            if (request.EndDate.HasValue)
            {
                leaveRequests = leaveRequests.Where(lr => lr.ToDate <= request.EndDate.Value);
            }

            var requestDtos = leaveRequests
                .Where(lr => lr.User != null) // Filter out records with null User navigation property (LeaveType can be null in simplified system)
                .Select(lr => new LeaveRequestDto
                {
                    Id = lr.Id,
                    User = new UserSummaryDto
                    {
                        Id = lr.User.Id,
                        Name = lr.User.Name,
                        EmployeeId = lr.User.EmployeeDetail?.EmployeeId
                    },
                    LeaveType = lr.LeaveType?.Name ?? "Leave Request", // Handle null LeaveType for simplified system
                    FromDate = lr.FromDate,
                    ToDate = lr.ToDate,
                    TotalDays = lr.TotalDays,
                    Status = lr.Status.ToString().ToLowerInvariant(),
                    Reason = lr.Reason,
                    AppliedDate = lr.CreatedAt,
                    ApprovedBy = lr.Approver?.Name,
                    ApprovedAt = lr.ApprovedAt,
                    Comments = lr.Comments
                }).ToList();

            var response = new LeaveRequestsResponseDto
            {
                Requests = requestDtos
            };

            return ApiResponse<LeaveRequestsResponseDto>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting leave requests");
            return ApiResponse<LeaveRequestsResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while getting leave requests");
        }
    }

    public async Task<ApiResponse<LeaveRequestDto>> UpdateLeaveStatusAsync(Guid leaveRequestId, string userId, UpdateLeaveStatusDto request)
    {
        try
        {
            var leaveRequest = await _unitOfWork.Leaves.GetByIdAsync(leaveRequestId);
            if (leaveRequest == null)
            {
                return ApiResponse<LeaveRequestDto>.ErrorResult("LEAVE_REQUEST_NOT_FOUND", "Leave request not found");
            }

            if (leaveRequest.Status != LeaveStatus.Pending)
            {
                return ApiResponse<LeaveRequestDto>.ErrorResult("INVALID_STATUS", "Only pending leave requests can be updated");
            }

            // Parse and validate status
            if (!Enum.TryParse<LeaveStatus>(request.Status, true, out var newStatus))
            {
                return ApiResponse<LeaveRequestDto>.ErrorResult("INVALID_STATUS", "Invalid status value");
            }

            // Update leave request
            leaveRequest.Status = newStatus;
            leaveRequest.ApprovedBy = Guid.Parse(userId);
            leaveRequest.ApprovedAt = DateTime.UtcNow;
            leaveRequest.Comments = request.Comments;

            await _unitOfWork.Leaves.UpdateAsync(leaveRequest);

            // If approved, deduct from monthly allowance
            if (newStatus == LeaveStatus.Approved)
            {
                var leaveMonth = leaveRequest.FromDate.Month;
                var leaveYear = leaveRequest.FromDate.Year;

                // Ensure monthly allowance exists
                await _unitOfWork.MonthlyLeaveAllowances.CreateOrUpdateAllowanceAsync(leaveRequest.UserId, leaveYear, leaveMonth);

                // Deduct from monthly allowance
                var deductionSuccess = await _unitOfWork.MonthlyLeaveAllowances.DeductLeaveAsync(
                    leaveRequest.UserId, leaveRequest.Id, leaveYear, leaveMonth, (int)leaveRequest.TotalDays);

                if (!deductionSuccess)
                {
                    return ApiResponse<LeaveRequestDto>.ErrorResult("INSUFFICIENT_BALANCE",
                        "Unable to approve: Insufficient monthly leave balance");
                }
            }
            // If rejected and was previously approved, restore monthly allowance
            else if (newStatus == LeaveStatus.Rejected)
            {
                await _unitOfWork.MonthlyLeaveAllowances.RestoreLeaveAsync(leaveRequest.Id);
            }

            await _unitOfWork.SaveChangesAsync();

            // Reload with related data
            var updatedRequest = await _unitOfWork.Leaves.GetByIdAsync(leaveRequestId);
            var responseDto = new LeaveRequestDto
            {
                Id = updatedRequest!.Id,
                User = new UserSummaryDto
                {
                    Id = updatedRequest.User.Id,
                    Name = updatedRequest.User.Name,
                    EmployeeId = updatedRequest.User.EmployeeDetail?.EmployeeId
                },
                LeaveType = updatedRequest.LeaveType?.Name ?? "Leave Request", // Handle null LeaveType for simplified system
                FromDate = updatedRequest.FromDate,
                ToDate = updatedRequest.ToDate,
                TotalDays = updatedRequest.TotalDays,
                Status = updatedRequest.Status.ToString().ToLowerInvariant(),
                Reason = updatedRequest.Reason,
                AppliedDate = updatedRequest.CreatedAt,
                ApprovedBy = updatedRequest.Approver?.Name,
                ApprovedAt = updatedRequest.ApprovedAt,
                Comments = updatedRequest.Comments
            };

            return ApiResponse<LeaveRequestDto>.SuccessResult(responseDto, "Leave request status updated successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating leave request status for request {LeaveRequestId}", leaveRequestId);
            return ApiResponse<LeaveRequestDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while updating leave request status");
        }
    }

    private int CalculateLeaveDays(DateTime fromDate, DateTime toDate, string durationType)
    {
        var totalDays = (toDate - fromDate).Days + 1;

        if (durationType.Equals("half-day", StringComparison.OrdinalIgnoreCase))
        {
            return Math.Max(1, totalDays / 2);
        }

        return totalDays;
    }

    // UpdateLeaveBalanceAsync method removed - now using MonthlyLeaveAllowance system

    public async Task<ApiResponse<OrganizationLeaveRequestsResponseDto>> GetOrganizationLeaveRequestsAsync(string organizationId, GetOrganizationLeaveRequestsDto request)
    {
        try
        {
            if (!Guid.TryParse(organizationId, out var orgGuid))
            {
                return ApiResponse<OrganizationLeaveRequestsResponseDto>.ErrorResult("INVALID_ORGANIZATION", "Invalid organization context");
            }

            var leaveRequests = await _unitOfWork.Leaves.GetByOrganizationAsync(orgGuid);

            // Apply filters
            var filteredRequests = leaveRequests.AsQueryable();

            if (!string.IsNullOrEmpty(request.Status))
            {
                if (Enum.TryParse<LeaveStatus>(request.Status, true, out var status))
                {
                    filteredRequests = filteredRequests.Where(lr => lr.Status == status);
                }
            }

            if (request.StartDate.HasValue)
            {
                filteredRequests = filteredRequests.Where(lr => lr.FromDate >= request.StartDate.Value);
            }

            if (request.EndDate.HasValue)
            {
                filteredRequests = filteredRequests.Where(lr => lr.ToDate <= request.EndDate.Value);
            }

            if (request.EmployeeId.HasValue)
            {
                filteredRequests = filteredRequests.Where(lr => lr.UserId == request.EmployeeId.Value);
            }

            if (!string.IsNullOrEmpty(request.Department))
            {
                filteredRequests = filteredRequests.Where(lr =>
                    lr.User.EmployeeDetail != null &&
                    lr.User.EmployeeDetail.Department.ToLower().Contains(request.Department.ToLower()));
            }

            var totalCount = filteredRequests.Count();
            var pagedRequests = filteredRequests
                .OrderByDescending(lr => lr.CreatedAt)
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToList();

            var requestDtos = pagedRequests.Select(lr => new LeaveRequestDto
            {
                Id = lr.Id,
                User = new UserSummaryDto
                {
                    Id = lr.User.Id,
                    Name = lr.User.Name,
                    EmployeeId = lr.User.EmployeeDetail?.EmployeeId
                },
                LeaveType = lr.LeaveType?.Name ?? "Leave Request", // Handle null LeaveType for simplified system
                FromDate = lr.FromDate,
                ToDate = lr.ToDate,
                TotalDays = lr.TotalDays,
                Status = lr.Status.ToString().ToLowerInvariant(),
                Reason = lr.Reason,
                AppliedDate = lr.CreatedAt,
                ApprovedBy = lr.Approver?.Name,
                ApprovedAt = lr.ApprovedAt,
                Comments = lr.Comments
            }).ToList();

            var response = new OrganizationLeaveRequestsResponseDto
            {
                Requests = requestDtos,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize
            };

            return ApiResponse<OrganizationLeaveRequestsResponseDto>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting organization leave requests for organization {OrganizationId}", organizationId);
            return ApiResponse<OrganizationLeaveRequestsResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while getting leave requests");
        }
    }

    public async Task<ApiResponse<LeaveStatisticsResponseDto>> GetLeaveStatisticsAsync(string organizationId, GetLeaveStatisticsRequestDto request)
    {
        try
        {
            if (!Guid.TryParse(organizationId, out var orgGuid))
            {
                return ApiResponse<LeaveStatisticsResponseDto>.ErrorResult("INVALID_ORGANIZATION", "Invalid organization context");
            }

            var leaveRequests = await _unitOfWork.Leaves.GetByOrganizationAsync(orgGuid);

            // Apply date filters
            var filteredRequests = leaveRequests.AsQueryable();
            if (request.StartDate.HasValue)
            {
                filteredRequests = filteredRequests.Where(lr => lr.FromDate >= request.StartDate.Value);
            }
            if (request.EndDate.HasValue)
            {
                filteredRequests = filteredRequests.Where(lr => lr.ToDate <= request.EndDate.Value);
            }
            if (!string.IsNullOrEmpty(request.Department))
            {
                filteredRequests = filteredRequests.Where(lr =>
                    lr.User.EmployeeDetail != null &&
                    lr.User.EmployeeDetail.Department.ToLower().Contains(request.Department.ToLower()));
            }

            var requestsList = filteredRequests.ToList();
            var totalEmployees = await _unitOfWork.Users.CountAsync(u => u.OrganizationId == orgGuid && u.Role == UserRole.Employee);

            var statistics = new LeaveStatisticsDto
            {
                TotalRequests = requestsList.Count,
                PendingRequests = requestsList.Count(lr => lr.Status == LeaveStatus.Pending),
                ApprovedRequests = requestsList.Count(lr => lr.Status == LeaveStatus.Approved),
                RejectedRequests = requestsList.Count(lr => lr.Status == LeaveStatus.Rejected),
                TotalLeaveDays = requestsList.Where(lr => lr.Status == LeaveStatus.Approved).Sum(lr => lr.TotalDays),
                AverageLeavePerEmployee = totalEmployees > 0 ?
                    (double)requestsList.Where(lr => lr.Status == LeaveStatus.Approved).Sum(lr => lr.TotalDays) / totalEmployees : 0,

                LeaveTypeBreakdown = requestsList
                    .GroupBy(lr => lr.LeaveType?.Name ?? "Leave Request") // Handle null LeaveType for simplified system
                    .Select(g => new LeaveTypeStatisticsDto
                    {
                        LeaveType = g.Key,
                        RequestCount = g.Count(),
                        TotalDays = g.Where(lr => lr.Status == LeaveStatus.Approved).Sum(lr => lr.TotalDays)
                    }).ToList(),

                DepartmentBreakdown = requestsList
                    .Where(lr => lr.User.EmployeeDetail != null)
                    .GroupBy(lr => lr.User.EmployeeDetail!.Department)
                    .Select(g => new DepartmentLeaveStatisticsDto
                    {
                        Department = g.Key,
                        RequestCount = g.Count(),
                        TotalDays = g.Where(lr => lr.Status == LeaveStatus.Approved).Sum(lr => lr.TotalDays),
                        UtilizationRate = g.Count() > 0 ? (double)g.Count(lr => lr.Status == LeaveStatus.Approved) / g.Count() * 100 : 0
                    }).ToList()
            };

            var response = new LeaveStatisticsResponseDto { Statistics = statistics };
            return ApiResponse<LeaveStatisticsResponseDto>.SuccessResult(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting leave statistics for organization {OrganizationId}", organizationId);
            return ApiResponse<LeaveStatisticsResponseDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while getting leave statistics");
        }
    }

    public async Task<ApiResponse<LeaveRequestDto>> ApproveRejectLeaveAsync(Guid leaveRequestId, string adminUserId, ApproveRejectLeaveDto request)
    {
        try
        {
            var leaveRequest = await _unitOfWork.Leaves.GetByIdAsync(leaveRequestId);
            if (leaveRequest == null)
            {
                return ApiResponse<LeaveRequestDto>.ErrorResult("LEAVE_REQUEST_NOT_FOUND", "Leave request not found");
            }

            if (leaveRequest.Status != LeaveStatus.Pending)
            {
                return ApiResponse<LeaveRequestDto>.ErrorResult("INVALID_STATUS", "Only pending leave requests can be approved or rejected");
            }

            // Parse and validate status
            if (!Enum.TryParse<LeaveStatus>(request.Status, true, out var newStatus) ||
                (newStatus != LeaveStatus.Approved && newStatus != LeaveStatus.Rejected))
            {
                return ApiResponse<LeaveRequestDto>.ErrorResult("INVALID_STATUS", "Status must be 'approved' or 'rejected'");
            }

            // Update leave request
            leaveRequest.Status = newStatus;
            leaveRequest.ApprovedBy = Guid.Parse(adminUserId);
            leaveRequest.ApprovedAt = DateTime.UtcNow;
            leaveRequest.Comments = request.Comments;

            await _unitOfWork.Leaves.UpdateAsync(leaveRequest);

            // If approved, deduct from monthly allowance
            if (newStatus == LeaveStatus.Approved)
            {
                var leaveMonth = leaveRequest.FromDate.Month;
                var leaveYear = leaveRequest.FromDate.Year;

                // Ensure monthly allowance exists
                await _unitOfWork.MonthlyLeaveAllowances.CreateOrUpdateAllowanceAsync(leaveRequest.UserId, leaveYear, leaveMonth);

                // Deduct from monthly allowance
                var deductionSuccess = await _unitOfWork.MonthlyLeaveAllowances.DeductLeaveAsync(
                    leaveRequest.UserId, leaveRequest.Id, leaveYear, leaveMonth, (int)leaveRequest.TotalDays);

                if (!deductionSuccess)
                {
                    return ApiResponse<LeaveRequestDto>.ErrorResult("INSUFFICIENT_BALANCE",
                        "Unable to approve: Insufficient monthly leave balance");
                }
            }
            // If rejected and was previously approved, restore monthly allowance
            else if (newStatus == LeaveStatus.Rejected)
            {
                await _unitOfWork.MonthlyLeaveAllowances.RestoreLeaveAsync(leaveRequest.Id);
            }

            await _unitOfWork.SaveChangesAsync();

            // Reload with related data
            var updatedRequest = await _unitOfWork.Leaves.GetByIdAsync(leaveRequestId);
            var responseDto = new LeaveRequestDto
            {
                Id = updatedRequest!.Id,
                User = new UserSummaryDto
                {
                    Id = updatedRequest.User.Id,
                    Name = updatedRequest.User.Name,
                    EmployeeId = updatedRequest.User.EmployeeDetail?.EmployeeId
                },
                LeaveType = updatedRequest.LeaveType?.Name ?? "Leave Request", // Handle null LeaveType for simplified system
                FromDate = updatedRequest.FromDate,
                ToDate = updatedRequest.ToDate,
                TotalDays = updatedRequest.TotalDays,
                Status = updatedRequest.Status.ToString().ToLowerInvariant(),
                Reason = updatedRequest.Reason,
                AppliedDate = updatedRequest.CreatedAt,
                ApprovedBy = updatedRequest.Approver?.Name,
                ApprovedAt = updatedRequest.ApprovedAt,
                Comments = updatedRequest.Comments
            };

            return ApiResponse<LeaveRequestDto>.SuccessResult(responseDto, $"Leave request {newStatus.ToString().ToLowerInvariant()} successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error approving/rejecting leave request {LeaveRequestId}", leaveRequestId);
            return ApiResponse<LeaveRequestDto>.ErrorResult("INTERNAL_SERVER_ERROR", "An error occurred while processing leave request");
        }
    }
}
