
import { useEffect, useMemo, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import { Clock, MapPin, Play, Square, Calendar as CalendarIcon } from 'lucide-react';
import { toast } from 'sonner';
import apiService from '@/services/api';

interface AttendanceRecordUI {
  date: string;
  checkIn: string | '-';
  checkOut: string | '-';
  hours: string;
  status: string;
}

const AttendanceManagement = () => {
  const [isCheckedIn, setIsCheckedIn] = useState(false);
  const [todayCheckIn, setTodayCheckIn] = useState<string | null>(null);
  const [workHoursToday, setWorkHoursToday] = useState<string>('0h');
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [records, setRecords] = useState<AttendanceRecordUI[]>([]);
  const [summary, setSummary] = useState<{ present: number; absent: number; late: number; totalHours: number }>({ present: 0, absent: 0, late: 0, totalHours: 0 });
  const [loading, setLoading] = useState(false);

  const monthRange = useMemo(() => {
    const now = new Date();
    const start = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), 1));
    const end = new Date();
    return { start, end };
  }, []);

  // IST formatting functions
  const formatTime = (iso?: string | null) => {
    if (!iso) return '-';
    try {
      const date = new Date(iso);
      return date.toLocaleTimeString('en-IN', {
        timeZone: 'Asia/Kolkata',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    } catch (error) {
      console.error('Error formatting IST time:', error);
      return 'Invalid Time';
    }
  };

  const formatISTDateTime = (dateTimeString: string) => {
    try {
      const date = new Date(dateTimeString);
      return date.toLocaleString('en-IN', {
        timeZone: 'Asia/Kolkata',
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    } catch (error) {
      console.error('Error formatting IST datetime:', error);
      return 'Invalid DateTime';
    }
  };

  const getCurrentISTTime = () => {
    return new Date().toLocaleString('en-IN', {
      timeZone: 'Asia/Kolkata'
    });
  };

  const fmtDate = (iso: string) => new Date(iso).toLocaleDateString();

  const loadAttendance = async () => {
    try {
      setLoading(true);
      const res = await apiService.getAttendanceRecords({
        startDate: monthRange.start.toISOString(),
        endDate: monthRange.end.toISOString(),
      });
      if (res.success && res.data) {
        const ui: AttendanceRecordUI[] = res.data.records.map((r: any) => ({
          date: fmtDate(r.date),
          checkIn: r.checkInTime ? formatTime(r.checkInTime) : '-',
          checkOut: r.checkOutTime ? formatTime(r.checkOutTime) : '-',
          hours: r.totalHours ? `${r.totalHours}h` : '0h',
          status: r.status?.charAt(0).toUpperCase() + r.status?.slice(1) || 'Unknown',
        }));
        setRecords(ui);
        setSummary({
          present: res.data.summary.presentDays,
          absent: res.data.summary.absentDays,
          late: res.data.summary.lateDays,
          totalHours: res.data.summary.totalHours,
        });

        // Determine today's status
        const todayStr = new Date().toDateString();
        const today = res.data.records.find((r: any) => new Date(r.date).toDateString() === todayStr);
        const checkedIn = !!today?.checkInTime && !today?.checkOutTime;
        setIsCheckedIn(checkedIn);
        setTodayCheckIn(today?.checkInTime ? formatTime(today.checkInTime) : null);
        setWorkHoursToday(today?.totalHours ? `${today.totalHours}h` : '0h');
      } else {
        toast.error(res.error?.message || 'Failed to load attendance');
      }
    } catch (e) {
      console.error(e);
      toast.error('Failed to load attendance');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAttendance();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleCheckIn = async () => {
    try {
      const ts = new Date().toISOString();
      const res = await apiService.checkIn(ts, 'Office');
      if (res.success) {
        toast.success('Checked in successfully');
        setIsCheckedIn(true);
        setTodayCheckIn(formatTime(res.data?.checkInTime || ts));
        await loadAttendance();
      } else {
        toast.error(res.error?.message || 'Check-in failed');
      }
    } catch (e) {
      toast.error('Check-in failed');
    }
  };

  const handleCheckOut = async () => {
    try {
      const ts = new Date().toISOString();
      const res = await apiService.checkOut(ts);
      if (res.success) {
        toast.success('Checked out successfully');
        setIsCheckedIn(false);
        await loadAttendance();
      } else {
        toast.error(res.error?.message || 'Check-out failed');
      }
    } catch (e) {
      toast.error('Check-out failed');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Attendance Management</h1>
        <p className="text-gray-600 mt-1">Track your daily attendance and work hours</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Check In/Out Card */}
        <Card className="lg:col-span-1 border-0 shadow-lg bg-gradient-to-br from-blue-500 to-purple-600 text-white">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="h-5 w-5" />
              <span>Today's Attendance</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <div className="text-2xl font-bold">
                {new Date().toLocaleTimeString('en-IN', {
                  timeZone: 'Asia/Kolkata',
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: true
                })}
              </div>
              <div className="text-sm opacity-90">
                {new Date().toLocaleDateString('en-IN', {
                  timeZone: 'Asia/Kolkata',
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </div>
              <div className="text-xs opacity-75 mt-1">
                Indian Standard Time (IST)
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Check In:</span>
                <span className="font-medium">{todayCheckIn ?? '--:--'}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Work Hours:</span>
                <span className="font-medium">{workHoursToday}</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4" />
                <span className="text-sm">Office Location</span>
              </div>
            </div>

            <div className="space-y-2">
              {!isCheckedIn ? (
                <Button
                  disabled={loading}
                  onClick={handleCheckIn}
                  className="w-full bg-white text-blue-600 hover:bg-gray-100"
                >
                  <Play className="h-4 w-4 mr-2" />
                  Check In
                </Button>
              ) : (
                <Button
                  disabled={loading}
                  onClick={handleCheckOut}
                  className="w-full bg-red-500 hover:bg-red-600 text-white"
                >
                  <Square className="h-4 w-4 mr-2" />
                  Check Out
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Calendar */}
        <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CalendarIcon className="h-5 w-5" />
              <span>Calendar</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              className="rounded-md border"
            />
          </CardContent>
        </Card>

        {/* Attendance Stats */}
        <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>This Month's Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Present Days</span>
              <span className="text-lg font-bold text-green-600">{summary.present}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Absent Days</span>
              <span className="text-lg font-bold text-red-600">{summary.absent}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Late Arrivals</span>
              <span className="text-lg font-bold text-orange-600">{summary.late}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Total Hours</span>
              <span className="text-lg font-bold text-blue-600">{summary.totalHours}h</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Attendance History */}
      <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle>Attendance History</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Date</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Check In</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Check Out</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Total Hours</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Status</th>
                </tr>
              </thead>
              <tbody>
                {records.map((record, index) => (
                  <tr key={index} className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                    <td className="py-3 px-4 text-gray-900">{record.date}</td>
                    <td className="py-3 px-4 text-gray-700">{record.checkIn}</td>
                    <td className="py-3 px-4 text-gray-700">{record.checkOut}</td>
                    <td className="py-3 px-4 text-gray-700">{record.hours}</td>
                    <td className="py-3 px-4">
                      <Badge
                        variant={
                          record.status === 'Present' ? 'default' :
                          record.status === 'Late' ? 'secondary' :
                          'destructive'
                        }
                      >
                        {record.status}
                      </Badge>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AttendanceManagement;
