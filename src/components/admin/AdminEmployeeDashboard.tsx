import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  ArrowLeft,
  Calendar, 
  Clock, 
  CheckSquare, 
  TrendingUp, 
  User,
  DollarSign,
  Bell,
  FileText,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { apiService } from '@/services/api';
import { EmployeeDashboard as EmployeeDashboardType } from '@/services/api';

interface AdminEmployeeDashboardProps {
  employeeId: string;
  employeeName: string;
  onBack: () => void;
}

const AdminEmployeeDashboard = ({ employeeId, employeeName, onBack }: AdminEmployeeDashboardProps) => {
  const [dashboardData, setDashboardData] = useState<EmployeeDashboardType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEmployeeDashboard = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await apiService.getEmployeeDashboardForAdmin(employeeId);

        if (response.success && response.data) {
          setDashboardData(response.data);
        } else {
          setError(response.error?.message || 'Failed to load employee dashboard data');
        }
      } catch (err) {
        setError('Failed to load employee dashboard data');
        console.error('Admin employee dashboard fetch error:', err);
      } finally {
        setLoading(false);
      }
    };

    if (employeeId) {
      fetchEmployeeDashboard();
    }
  }, [employeeId]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={onBack} className="flex items-center space-x-2">
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Employee Management</span>
          </Button>
        </div>
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          <span className="ml-2 text-gray-500">Loading employee dashboard...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={onBack} className="flex items-center space-x-2">
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Employee Management</span>
          </Button>
        </div>
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Dashboard</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={() => window.location.reload()}>Try Again</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={onBack} className="flex items-center space-x-2">
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Employee Management</span>
          </Button>
        </div>
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <User className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Data Available</h3>
              <p className="text-gray-600">Employee dashboard data is not available.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { metrics, recentActivities, profileSummary, upcomingTasks, attendanceSummary } = dashboardData;

  const quickActions = [
    { title: 'View Leave Requests', icon: Calendar, color: 'bg-blue-500' },
    { title: 'View Attendance', icon: Clock, color: 'bg-green-500' },
    { title: 'View Tasks', icon: CheckSquare, color: 'bg-purple-500' },
    { title: 'View Performance', icon: TrendingUp, color: 'bg-orange-500' }
  ];

  return (
    <div className="space-y-6">
      {/* Header with Back Button */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={onBack} className="flex items-center space-x-2">
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Employee Management</span>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Employee Dashboard</h1>
            <p className="text-gray-600">{employeeName} - Detailed Overview</p>
          </div>
        </div>
        <Badge variant="secondary" className="text-sm">
          Admin View
        </Badge>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {quickActions.map((action, index) => {
          const Icon = action.icon;
          return (
            <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="flex items-center space-x-3 p-4">
                <div className={`p-2 rounded-lg ${action.color}`}>
                  <Icon className="h-5 w-5 text-white" />
                </div>
                <span className="font-medium text-sm">{action.title}</span>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Leave Balance</CardTitle>
            <Calendar className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.remainingLeaves}</div>
            <p className="text-xs text-gray-600">{metrics.usedLeaves} used of {metrics.totalLeaves} total</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Hours</CardTitle>
            <Clock className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.todayHours}h</div>
            <p className="text-xs text-gray-600">Week: {metrics.weekHours}h</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Tasks</CardTitle>
            <CheckSquare className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.activeTasks}</div>
            <p className="text-xs text-gray-600">{metrics.completedTasks} completed this month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Performance</CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.performanceScore.toFixed(1)}/5</div>
            <p className="text-xs text-gray-600">
              {metrics.nextReviewDate ? `Next review: ${new Date(metrics.nextReviewDate).toLocaleDateString()}` : 'No review scheduled'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activities and Upcoming Tasks */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Recent Activities</CardTitle>
            <CardDescription>Latest employee activities and updates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.length > 0 ? (
                recentActivities.map((activity, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">{activity.message}</p>
                      <p className="text-xs text-gray-500">
                        {new Date(activity.timestamp).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500">No recent activities</p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Upcoming Tasks</CardTitle>
            <CardDescription>Tasks assigned to this employee</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {upcomingTasks.length > 0 ? (
                upcomingTasks.map((task, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <p className="font-medium text-sm">{task.title}</p>
                      <p className="text-xs text-gray-600">{task.description}</p>
                    </div>
                    <Badge variant={task.priority === 'high' ? 'destructive' : task.priority === 'medium' ? 'default' : 'secondary'}>
                      {task.priority}
                    </Badge>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500">No upcoming tasks</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Employee Profile Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Employee Profile Summary</CardTitle>
          <CardDescription>Overview of employee details and information</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-center space-x-3">
              <User className="h-8 w-8 text-blue-600" />
              <div>
                <p className="font-medium">{profileSummary.name}</p>
                <p className="text-sm text-gray-600">{profileSummary.jobTitle}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <DollarSign className="h-8 w-8 text-green-600" />
              <div>
                <p className="font-medium">{profileSummary.department}</p>
                <p className="text-sm text-gray-600">ID: {profileSummary.employeeId}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Calendar className="h-8 w-8 text-purple-600" />
              <div>
                <p className="font-medium">
                  {profileSummary.joinDate ? new Date(profileSummary.joinDate).toLocaleDateString() : 'N/A'}
                </p>
                <p className="text-sm text-gray-600">Join Date</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Attendance Summary */}
      {attendanceSummary && (
        <Card>
          <CardHeader>
            <CardTitle>Attendance Summary</CardTitle>
            <CardDescription>Employee attendance and work hours overview</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{attendanceSummary.presentDays}</div>
                <p className="text-sm text-gray-600">Present Days</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{attendanceSummary.absentDays}</div>
                <p className="text-sm text-gray-600">Absent Days</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{attendanceSummary.lateDays}</div>
                <p className="text-sm text-gray-600">Late Days</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{attendanceSummary.attendanceRate.toFixed(1)}%</div>
                <p className="text-sm text-gray-600">Attendance Rate</p>
              </div>
            </div>
            {attendanceSummary.isCheckedIn && (
              <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-sm text-green-800">
                  ✓ Currently checked in at {attendanceSummary.checkInTime ?
                    new Date(attendanceSummary.checkInTime).toLocaleTimeString('en-IN', {
                      timeZone: 'Asia/Kolkata',
                      hour: '2-digit',
                      minute: '2-digit',
                      hour12: true
                    }) : 'N/A'} IST
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AdminEmployeeDashboard;
