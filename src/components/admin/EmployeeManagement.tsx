import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Users, UserPlus, Edit, Trash2, Eye, EyeOff, Settings, ChevronDown, ChevronRight, Loader2, Search, Filter, Download, MoreHorizontal, Building2, User, Copy, Key, Shield, AlertCircle, CheckCircle, BarChart3 } from 'lucide-react';
import { toast } from 'sonner';
import { apiService } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';
import AdminEmployeeDashboard from './AdminEmployeeDashboard';

// Password strength calculation
const calculatePasswordStrength = (password: string) => {
  if (!password) return { score: 0, strength: 'Very Weak', color: 'bg-red-500' };

  let score = 0;
  const checks = {
    length: password.length >= 8,
    uppercase: /[A-Z]/.test(password),
    lowercase: /[a-z]/.test(password),
    numbers: /\d/.test(password),
    special: /[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)
  };

  // Length scoring
  if (password.length >= 8) score += 20;
  if (password.length >= 12) score += 10;

  // Character variety
  if (checks.uppercase) score += 15;
  if (checks.lowercase) score += 15;
  if (checks.numbers) score += 15;
  if (checks.special) score += 15;

  // Bonus for variety
  const uniqueChars = new Set(password).size;
  score += Math.min(10, uniqueChars);

  let strength = 'Very Weak';
  let color = 'bg-red-500';

  if (score >= 90) { strength = 'Very Strong'; color = 'bg-green-600'; }
  else if (score >= 75) { strength = 'Strong'; color = 'bg-green-500'; }
  else if (score >= 60) { strength = 'Good'; color = 'bg-yellow-500'; }
  else if (score >= 40) { strength = 'Fair'; color = 'bg-orange-500'; }
  else if (score >= 20) { strength = 'Weak'; color = 'bg-red-400'; }

  return { score: Math.min(100, score), strength, color };
};

// Fixed Select component empty value issue - v2
const EmployeeManagement: React.FC = () => {
  const { user } = useAuth();
  const [showAddEmployee, setShowAddEmployee] = useState(false);
  const [showEditEmployee, setShowEditEmployee] = useState(false);
  const [showEmployeeDetails, setShowEmployeeDetails] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [activeTab, setActiveTab] = useState('employees');
  const [selectedEmployee, setSelectedEmployee] = useState<any>(null);
  const [employeeToDelete, setEmployeeToDelete] = useState<any>(null);
  const [expandedDepts, setExpandedDepts] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [employees, setEmployees] = useState<any[]>([]);
  const [organizationStructure, setOrganizationStructure] = useState<any>({});
  const [employeeStats, setEmployeeStats] = useState<any>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [filterDepartment, setFilterDepartment] = useState('');
  const [filterRole, setFilterRole] = useState('');
  const [filterEmploymentType, setFilterEmploymentType] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [generatingCredentials, setGeneratingCredentials] = useState(false);
  const [showGenerateCredentialsModal, setShowGenerateCredentialsModal] = useState(false);
  const [customPassword, setCustomPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [useCustomPassword, setUseCustomPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState({ score: 0, strength: 'Very Weak', color: 'bg-red-500' });
  const [showEmployeePassword, setShowEmployeePassword] = useState(false);
  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);
  const [newPassword, setNewPassword] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [changingPassword, setChangingPassword] = useState(false);
  const [newPasswordStrength, setNewPasswordStrength] = useState({ score: 0, strength: 'Very Weak', color: 'bg-red-500' });

  // Dashboard view state
  const [showEmployeeDashboard, setShowEmployeeDashboard] = useState(false);
  const [dashboardEmployee, setDashboardEmployee] = useState<any>(null);

  // Update password strength when custom password changes
  useEffect(() => {
    if (useCustomPassword && customPassword) {
      setPasswordStrength(calculatePasswordStrength(customPassword));
    }
  }, [customPassword, useCustomPassword]);

  // Update password strength when new password changes
  useEffect(() => {
    if (newPassword) {
      setNewPasswordStrength(calculatePasswordStrength(newPassword));
    }
  }, [newPassword]);

  // Handle viewing employee details
  const handleViewEmployee = async (employee: any) => {
    try {
      // Fetch complete employee details from API
      const response = await apiService.getEmployeeById(employee.id);

      if (response.success && response.data) {
        setSelectedEmployee(response.data);
        console.log('Employee details loaded:', {
          loginUsername: response.data.loginUsername,
          employeeDetailsLoginUsername: response.data.employeeDetails?.loginUsername,
          hasCredentials: !!(response.data.loginUsername || response.data.employeeDetails?.loginUsername)
        });
      } else {
        // Fallback to list data if API call fails
        setSelectedEmployee(employee);
        console.warn('Failed to fetch complete employee details, using list data');
      }
    } catch (error) {
      // Fallback to list data if API call fails
      setSelectedEmployee(employee);
      console.error('Error fetching employee details:', error);
    }

    setShowEmployeePassword(false); // Reset password visibility
    setShowEmployeeDetails(true);
  };

  // Handle viewing employee dashboard
  const handleViewEmployeeDashboard = (employee: any) => {
    setDashboardEmployee(employee);
    setShowEmployeeDashboard(true);
  };

  // Handle password change
  const handleChangePassword = async (employeeId: string, newPassword: string) => {
    try {
      setChangingPassword(true);

      const response = await apiService.changeEmployeePassword(employeeId, newPassword);

      if (response.success && response.data) {
        // Update the selected employee with the new password
        setSelectedEmployee((prev: any) => ({
          ...prev,
          loginUsername: response.data.loginUsername,
          loginTemporaryPassword: response.data.loginTemporaryPassword,
          requirePasswordReset: response.data.requirePasswordReset,
          passwordGeneratedAt: response.data.passwordGeneratedAt,
          employeeDetails: {
            ...prev.employeeDetails,
            loginUsername: response.data.loginUsername,
            loginTemporaryPassword: response.data.loginTemporaryPassword,
            requirePasswordReset: response.data.requirePasswordReset,
            passwordGeneratedAt: response.data.passwordGeneratedAt
          }
        }));

        // Also update the employee in the employees list
        setEmployees(prev => prev.map(emp =>
          emp.id === employeeId
            ? {
                ...emp,
                loginUsername: response.data.loginUsername,
                loginTemporaryPassword: response.data.loginTemporaryPassword,
                requirePasswordReset: response.data.requirePasswordReset,
                passwordGeneratedAt: response.data.passwordGeneratedAt,
                employeeDetails: {
                  ...emp.employeeDetails,
                  loginUsername: response.data.loginUsername,
                  loginTemporaryPassword: response.data.loginTemporaryPassword,
                  requirePasswordReset: response.data.requirePasswordReset,
                  passwordGeneratedAt: response.data.passwordGeneratedAt
                }
              }
            : emp
        ));

        toast.success('Employee password changed successfully!');

        // Close modal and reset form
        setShowChangePasswordModal(false);
        setNewPassword('');
        setShowNewPassword(false);
      } else {
        toast.error(response.error?.message || 'Failed to change password');
      }
    } catch (error) {
      console.error('Error changing password:', error);
      toast.error('Failed to change password');
    } finally {
      setChangingPassword(false);
    }
  };

  // Hierarchy Management State
  const [hierarchyEmployees, setHierarchyEmployees] = useState<any[]>([]);
  const [selectedEmployeeForAssignment, setSelectedEmployeeForAssignment] = useState<any>(null);
  const [selectedManager, setSelectedManager] = useState<any>(null);
  const [showAssignManagerDialog, setShowAssignManagerDialog] = useState(false);
  const [draggedEmployee, setDraggedEmployee] = useState<any>(null);
  const [hierarchyLoading, setHierarchyLoading] = useState(false);

  // Load employees from API
  useEffect(() => {
    loadEmployees();
    loadEmployeeStats();
    loadOrganizationHierarchy();
    if (activeTab === 'manage-hierarchy') {
      loadHierarchyEmployees();
    }
  }, [currentPage, searchTerm, filterDepartment, filterRole, filterEmploymentType, activeTab]);

  const loadEmployees = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        limit: 10,
        search: searchTerm || undefined,
        department: filterDepartment || undefined,
        role: filterRole || undefined,
        employmentType: filterEmploymentType || undefined,
      };

      const response = await apiService.getEmployees(params);

      if (response.success && response.data) {
        setEmployees(response.data.items || []);
        setTotalPages(response.data.pagination?.totalPages || 1);
      } else {
        toast.error('Failed to load employees');
      }
    } catch (error) {
      console.error('Error loading employees:', error);
      toast.error('Failed to load employees');
    } finally {
      setLoading(false);
    }
  };

  const loadEmployeeStats = async () => {
    try {
      const response = await apiService.getEmployeeStats();
      if (response.success && response.data) {
        setEmployeeStats(response.data);
      }
    } catch (error) {
      console.error('Error loading employee stats:', error);
    }
  };

  const loadOrganizationHierarchy = async () => {
    try {
      const response = await apiService.getOrganizationHierarchy();
      if (response.success && response.data && response.data.departments) {
        // Convert departments array to object structure expected by the UI
        const structureObj = response.data.departments.reduce((acc: any, dept: any) => {
          acc[dept.name] = {
            name: dept.name,
            manager: dept.manager,
            employees: dept.employees || []
          };
          return acc;
        }, {});
        setOrganizationStructure(structureObj);
      } else {
        // Set empty structure if no data
        setOrganizationStructure({});
      }
    } catch (error) {
      console.error('Error loading organization hierarchy:', error);
      setOrganizationStructure({});
    }
  };

  const allEmployees = Object.values(organizationStructure || {}).flatMap((dept: any) =>
    (dept?.employees || []).map((emp: any) => ({
      ...emp,
      department: dept?.name || 'Unknown',
      manager: dept?.manager || 'N/A'
    }))
  );

  const jobRoles = [
    'Senior Developer', 'Frontend Developer', 'Backend Developer', 'Intern Developer',
    'HR Specialist', 'Recruiter', 'Sales Manager', 'Sales Executive', 'Marketing Manager',
    'Product Manager', 'Designer', 'QA Engineer'
  ].filter(role => role && role.trim() !== ''); // Filter out any empty strings

  const permissions = [
    { id: 'task_assign', name: 'Assign Tasks', description: 'Can assign tasks to subordinates' },
    { id: 'task_manage', name: 'Manage Tasks', description: 'Can create, edit, delete tasks' },
    { id: 'view_reports', name: 'View Reports', description: 'Can view performance reports' },
    { id: 'manage_leave', name: 'Manage Leave', description: 'Can approve/reject leave requests' },
    { id: 'view_payroll', name: 'View Payroll', description: 'Can view salary information' }
  ];

  const toggleDepartment = (dept: string) => {
    setExpandedDepts(prev => 
      prev.includes(dept) 
        ? prev.filter(d => d !== dept)
        : [...prev, dept]
    );
  };

  // Validation functions
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    if (!phone) return true; // Optional field
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  };

  const validatePAN = (pan: string): boolean => {
    if (!pan) return true; // Optional field
    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    return panRegex.test(pan.toUpperCase());
  };

  const validateAadhar = (aadhar: string): boolean => {
    if (!aadhar) return true; // Optional field
    const aadharRegex = /^[0-9]{12}$/;
    return aadharRegex.test(aadhar.replace(/\s/g, ''));
  };

  const validateIFSC = (ifsc: string): boolean => {
    if (!ifsc) return true; // Optional field
    const ifscRegex = /^[A-Z]{4}0[A-Z0-9]{6}$/;
    return ifscRegex.test(ifsc.toUpperCase());
  };

  const handleAddEmployee = async (e: React.FormEvent) => {
    e.preventDefault();

    const formData = new FormData(e.target as HTMLFormElement);

    // Validate required fields
    const name = formData.get('employeeName') as string;
    const email = formData.get('employeeEmail') as string;
    const jobTitle = formData.get('jobRole') as string;
    const phone = formData.get('phone') as string;
    const pan = formData.get('pan') as string;
    const aadhar = formData.get('aadhar') as string;
    const bankIFSC = formData.get('bankIFSC') as string;

    // Validation checks
    if (!name?.trim()) {
      toast.error('Employee name is required');
      return;
    }

    if (!email?.trim()) {
      toast.error('Email is required');
      return;
    }

    if (!validateEmail(email)) {
      toast.error('Please enter a valid email address');
      return;
    }

    if (!jobTitle?.trim()) {
      toast.error('Job title is required');
      return;
    }

    if (!validatePhone(phone)) {
      toast.error('Please enter a valid phone number');
      return;
    }

    if (!validatePAN(pan)) {
      toast.error('Please enter a valid PAN number (e.g., **********)');
      return;
    }

    if (!validateAadhar(aadhar)) {
      toast.error('Please enter a valid Aadhar number (12 digits)');
      return;
    }

    if (!validateIFSC(bankIFSC)) {
      toast.error('Please enter a valid IFSC code (e.g., ABCD0123456)');
      return;
    }

    const baseSalary = parseFloat(formData.get('salary') as string) || 0;
    const annualCTC = parseFloat(formData.get('annualCTC') as string) || (baseSalary * 12);

    const employeeData = {
      name: formData.get('employeeName') as string,
      email: formData.get('employeeEmail') as string,
      role: 'employee',
      employeeDetails: {
        employeeId: `EMP${Date.now()}`,
        jobTitle: formData.get('jobRole') as string,
        department: formData.get('department') as string || 'General',
        joinDate: new Date().toISOString(),
        employmentType: formData.get('employmentType') as string || 'FullTime',
        workLocation: formData.get('workLocation') as string,
        baseSalary: baseSalary,
        annualCTC: annualCTC,
        phone: formData.get('phone') as string,
        address: formData.get('address') as string,
        dateOfBirth: formData.get('dateOfBirth') as string,
        emergencyContactName: formData.get('emergencyContactName') as string,
        emergencyContactPhone: formData.get('emergencyContactPhone') as string,
        emergencyContactRelation: formData.get('emergencyContactRelation') as string,
        // Additional personal information
        gender: formData.get('gender') as string,
        maritalStatus: formData.get('maritalStatus') as string,
        bloodGroup: formData.get('bloodGroup') as string,
        nationality: formData.get('nationality') as string,
        // Banking information
        bankName: formData.get('bankName') as string,
        bankAccountNumber: formData.get('bankAccountNumber') as string,
        bankIFSC: formData.get('bankIFSC') as string,
        // Government IDs
        pan: formData.get('pan') as string,
        aadhar: formData.get('aadhar') as string,
      }
    };

    try {
      const response = await apiService.createEmployeeAdvanced(employeeData);
      if (response.success) {
        toast.success('Employee added successfully!');
        setShowAddEmployee(false);
        loadEmployees();
        loadEmployeeStats();
        loadOrganizationHierarchy();
        // Reset form
        (e.target as HTMLFormElement).reset();
      } else {
        toast.error(response.error?.message || 'Failed to add employee');
      }
    } catch (error) {
      console.error('Error adding employee:', error);
      toast.error('Failed to add employee');
    }
  };

  const handleEditEmployee = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedEmployee) return;

    // Prevent users from editing their own records
    if (user && selectedEmployee.id === user.id) {
      toast.error('You cannot edit your own employee record. Please contact another administrator.');
      return;
    }

    const formData = new FormData(e.target as HTMLFormElement);

    // Validate required fields
    const name = formData.get('employeeName') as string;
    const email = formData.get('employeeEmail') as string;
    const jobTitle = formData.get('jobRole') as string;
    const phone = formData.get('phone') as string;
    const pan = formData.get('pan') as string;
    const aadhar = formData.get('aadhar') as string;
    const bankIFSC = formData.get('bankIFSC') as string;

    // Validation checks
    if (!name?.trim()) {
      toast.error('Employee name is required');
      return;
    }

    if (!email?.trim()) {
      toast.error('Email is required');
      return;
    }

    if (!validateEmail(email)) {
      toast.error('Please enter a valid email address');
      return;
    }

    if (!jobTitle?.trim()) {
      toast.error('Job title is required');
      return;
    }

    if (!validatePhone(phone)) {
      toast.error('Please enter a valid phone number');
      return;
    }

    if (!validatePAN(pan)) {
      toast.error('Please enter a valid PAN number (e.g., **********)');
      return;
    }

    if (!validateAadhar(aadhar)) {
      toast.error('Please enter a valid Aadhar number (12 digits)');
      return;
    }

    if (!validateIFSC(bankIFSC)) {
      toast.error('Please enter a valid IFSC code (e.g., ABCD0123456)');
      return;
    }

    const baseSalary = parseFloat(formData.get('salary') as string) || undefined;
    const annualCTC = parseFloat(formData.get('annualCTC') as string) || undefined;

    const updateData = {
      name: formData.get('employeeName') as string,
      email: formData.get('employeeEmail') as string,
      role: 'employee',
      employeeDetails: {
        jobTitle: formData.get('jobRole') as string,
        department: formData.get('department') as string,
        employmentType: formData.get('employmentType') as string,
        workLocation: formData.get('workLocation') as string,
        baseSalary: baseSalary,
        annualCTC: annualCTC,
        phone: formData.get('phone') as string,
        address: formData.get('address') as string,
        dateOfBirth: formData.get('dateOfBirth') as string,
        emergencyContactName: formData.get('emergencyContactName') as string,
        emergencyContactPhone: formData.get('emergencyContactPhone') as string,
        emergencyContactRelation: formData.get('emergencyContactRelation') as string,
        // Additional personal information
        gender: formData.get('gender') as string,
        maritalStatus: formData.get('maritalStatus') as string,
        bloodGroup: formData.get('bloodGroup') as string,
        nationality: formData.get('nationality') as string,
        // Banking information
        bankName: formData.get('bankName') as string,
        bankAccountNumber: formData.get('bankAccountNumber') as string,
        bankIFSC: formData.get('bankIFSC') as string,
        // Government IDs
        pan: formData.get('pan') as string,
        aadhar: formData.get('aadhar') as string,
      }
    };

    try {
      const response = await apiService.updateEmployee(selectedEmployee.id, updateData);
      if (response.success) {
        toast.success('Employee updated successfully!');
        setShowEditEmployee(false);
        setSelectedEmployee(null);
        loadEmployees();
        loadEmployeeStats();
        loadOrganizationHierarchy();
      } else {
        toast.error(response.error?.message || 'Failed to update employee');
      }
    } catch (error) {
      console.error('Error updating employee:', error);
      toast.error('Failed to update employee');
    }
  };

  const handleDeleteEmployee = (employee: any) => {
    // Prevent users from deleting their own records
    if (user && employee.id === user.id) {
      toast.error('You cannot delete your own employee record. Please contact another administrator.');
      return;
    }

    setEmployeeToDelete(employee);
    setShowDeleteConfirm(true);
  };

  const confirmDeleteEmployee = async () => {
    if (!employeeToDelete) return;

    try {
      const response = await apiService.deleteEmployee(employeeToDelete.id);
      if (response.success) {
        toast.success('Employee deleted successfully!');
        setShowDeleteConfirm(false);
        setEmployeeToDelete(null);
        loadEmployees();
        loadEmployeeStats();
        loadOrganizationHierarchy();
        if (activeTab === 'manage-hierarchy') {
          loadHierarchyEmployees();
        }
      } else {
        toast.error(response.error?.message || 'Failed to delete employee');
      }
    } catch (error) {
      console.error('Error deleting employee:', error);
      toast.error('Failed to delete employee');
    }
  };

  const handleGenerateCredentials = async (employeeId: string, customPassword?: string) => {
    try {
      setGeneratingCredentials(true);

      const requestBody = customPassword ? { customPassword } : {};
      const response = await apiService.generateEmployeeCredentials(employeeId, requestBody);

      if (response.success && response.data) {
        // Update the selected employee with the new credentials
        setSelectedEmployee((prev: any) => ({
          ...prev,
          loginUsername: response.data.loginUsername,
          loginTemporaryPassword: response.data.loginTemporaryPassword,
          requirePasswordReset: response.data.requirePasswordReset,
          passwordGeneratedAt: response.data.passwordGeneratedAt,
          employeeDetails: {
            ...prev.employeeDetails,
            loginUsername: response.data.loginUsername,
            requirePasswordReset: response.data.requirePasswordReset,
            passwordGeneratedAt: response.data.passwordGeneratedAt
          }
        }));

        // Also update the employee in the employees list
        setEmployees(prev => prev.map(emp =>
          emp.id === employeeId
            ? {
                ...emp,
                loginUsername: response.data.loginUsername,
                loginTemporaryPassword: response.data.loginTemporaryPassword,
                requirePasswordReset: response.data.requirePasswordReset,
                passwordGeneratedAt: response.data.passwordGeneratedAt,
                employeeDetails: {
                  ...emp.employeeDetails,
                  loginUsername: response.data.loginUsername,
                  requirePasswordReset: response.data.requirePasswordReset,
                  passwordGeneratedAt: response.data.passwordGeneratedAt
                }
              }
            : emp
        ));

        const passwordType = customPassword ? 'custom' : 'auto-generated';
        toast.success(`Login credentials generated successfully with ${passwordType} password!`);

        // Close modal and reset form
        setShowGenerateCredentialsModal(false);
        setCustomPassword('');
        setUseCustomPassword(false);
        setShowPassword(false);
      } else {
        toast.error(response.error?.message || 'Failed to generate credentials');
      }
    } catch (error) {
      console.error('Error generating credentials:', error);
      toast.error('Failed to generate credentials');
    } finally {
      setGeneratingCredentials(false);
    }
  };

  // Hierarchy Management Functions
  const loadHierarchyEmployees = async () => {
    try {
      setHierarchyLoading(true);
      const response = await apiService.getEmployees({ page: 1, limit: 1000 }); // Get all employees for hierarchy
      if (response.success && response.data) {
        setHierarchyEmployees(response.data.items || []);
      }
    } catch (error) {
      console.error('Error loading hierarchy employees:', error);
      toast.error('Failed to load employees for hierarchy management');
    } finally {
      setHierarchyLoading(false);
    }
  };

  const handleAssignManager = (employee: any) => {
    setSelectedEmployeeForAssignment(employee);
    setShowAssignManagerDialog(true);
  };

  const confirmAssignManager = async () => {
    if (!selectedEmployeeForAssignment || !selectedManager) return;

    // Basic client-side validation
    if (selectedEmployeeForAssignment.id === selectedManager.id) {
      toast.error('An employee cannot be their own manager');
      return;
    }

    try {
      const updateData = {
        employeeDetails: {
          managerId: selectedManager.id
        }
      };

      const response = await apiService.updateEmployee(selectedEmployeeForAssignment.id, updateData);
      if (response.success) {
        toast.success(`${selectedEmployeeForAssignment.name} has been assigned to ${selectedManager.name}`);
        setShowAssignManagerDialog(false);
        setSelectedEmployeeForAssignment(null);
        setSelectedManager(null);
        loadHierarchyEmployees();
        loadOrganizationHierarchy();
      } else {
        toast.error(response.error?.message || 'Failed to assign manager');
      }
    } catch (error) {
      console.error('Error assigning manager:', error);
      toast.error('Failed to assign manager');
    }
  };

  const handleRemoveManager = async (employee: any) => {
    try {
      const updateData = {
        employeeDetails: {
          managerId: null
        }
      };

      const response = await apiService.updateEmployee(employee.id, updateData);
      if (response.success) {
        toast.success(`${employee.name} has been removed from their manager`);
        loadHierarchyEmployees();
        loadOrganizationHierarchy();
      } else {
        toast.error(response.error?.message || 'Failed to remove manager');
      }
    } catch (error) {
      console.error('Error removing manager:', error);
      toast.error('Failed to remove manager');
    }
  };



  // Drag and Drop Handlers
  const handleDragStart = (e: React.DragEvent, employee: any) => {
    setDraggedEmployee(employee);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = async (e: React.DragEvent, targetManager: any) => {
    e.preventDefault();

    if (!draggedEmployee || !targetManager) return;

    // Basic client-side validation
    if (draggedEmployee.id === targetManager.id) {
      toast.error('An employee cannot be their own manager');
      setDraggedEmployee(null);
      return;
    }

    try {
      const updateData = {
        employeeDetails: {
          managerId: targetManager.id
        }
      };

      const response = await apiService.updateEmployee(draggedEmployee.id, updateData);
      if (response.success) {
        toast.success(`${draggedEmployee.name} has been assigned to ${targetManager.name}`);
        loadHierarchyEmployees();
        loadOrganizationHierarchy();
      } else {
        toast.error(response.error?.message || 'Failed to assign manager');
      }
    } catch (error) {
      console.error('Error in drag and drop assignment:', error);
      toast.error('Failed to assign manager');
    } finally {
      setDraggedEmployee(null);
    }
  };

  const getLevelColor = (level: string) => {
    switch (level?.toLowerCase()) {
      case 'senior': return 'bg-purple-100 text-purple-800';
      case 'mid': return 'bg-blue-100 text-blue-800';
      case 'junior': return 'bg-green-100 text-green-800';
      case 'intern': return 'bg-orange-100 text-orange-800';
      case 'fulltime': return 'bg-green-100 text-green-800';
      case 'parttime': return 'bg-yellow-100 text-yellow-800';
      case 'contract': return 'bg-orange-100 text-orange-800';
      case 'internship': return 'bg-blue-100 text-blue-800';
      case 'consultant': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Bulk operations
  const handleSelectEmployee = (employeeId: string, checked: boolean) => {
    if (checked) {
      setSelectedEmployees(prev => [...prev, employeeId]);
    } else {
      setSelectedEmployees(prev => prev.filter(id => id !== employeeId));
    }
  };

  const handleSelectAllEmployees = (checked: boolean) => {
    if (checked) {
      setSelectedEmployees(employees.map(emp => emp.id));
    } else {
      setSelectedEmployees([]);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedEmployees.length === 0) return;

    if (!confirm(`Are you sure you want to delete ${selectedEmployees.length} employees? This action cannot be undone.`)) {
      return;
    }

    try {
      const deletePromises = selectedEmployees.map(async (id) => {
        try {
          const response = await apiService.deleteEmployee(id);
          return { id, success: response.success, error: response.error };
        } catch (error) {
          return { id, success: false, error: { message: 'Network error' } };
        }
      });

      const results = await Promise.all(deletePromises);
      const successful = results.filter(r => r.success);
      const failed = results.filter(r => !r.success);

      if (successful.length > 0) {
        toast.success(`Successfully deleted ${successful.length} employee${successful.length > 1 ? 's' : ''}`);
      }

      if (failed.length > 0) {
        const errorMessages = failed.map(f => f.error?.message || 'Unknown error').join(', ');
        toast.error(`Failed to delete ${failed.length} employee${failed.length > 1 ? 's' : ''}: ${errorMessages}`);
      }

      setSelectedEmployees([]);
      // Refresh all components to reflect changes
      loadEmployees();
      loadEmployeeStats();
      loadOrganizationHierarchy();
      if (activeTab === 'manage-hierarchy') {
        loadHierarchyEmployees();
      }
    } catch (error) {
      console.error('Error in bulk delete:', error);
      toast.error('Failed to delete employees');
    }
  };

  const formatEmploymentType = (type: string) => {
    switch (type?.toLowerCase()) {
      case 'fulltime': return 'Full-Time';
      case 'parttime': return 'Part-Time';
      case 'contract': return 'Contract';
      case 'internship': return 'Internship';
      case 'consultant': return 'Consultant';
      default: return type || 'Full-Time';
    }
  };

  const formatEmploymentStatus = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active': return 'Active';
      case 'inactive': return 'Inactive';
      case 'onleave': return 'On Leave';
      case 'terminated': return 'Terminated';
      case 'probation': return 'Probation';
      default: return status || 'Active';
    }
  };



  // Show employee dashboard if selected
  if (showEmployeeDashboard && dashboardEmployee) {
    return (
      <AdminEmployeeDashboard
        employeeId={dashboardEmployee.id}
        employeeName={dashboardEmployee.name}
        onBack={() => {
          setShowEmployeeDashboard(false);
          setDashboardEmployee(null);
        }}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Employee Management</h1>
          <p className="text-gray-600 mt-1">Manage your organization's employees and permissions</p>
        </div>

        <Dialog open={showAddEmployee} onOpenChange={setShowAddEmployee}>
          <DialogTrigger asChild>
            <Button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
              <UserPlus className="h-4 w-4 mr-2" />
              Add Employee
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Add New Employee</DialogTitle>
              <DialogDescription>Add a new employee to your organization</DialogDescription>
            </DialogHeader>

            <form onSubmit={handleAddEmployee} className="space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Basic Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="employeeName">Full Name</Label>
                    <Input
                      id="employeeName"
                      name="employeeName"
                      placeholder="Enter employee name"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="employeeEmail">Email</Label>
                    <Input
                      id="employeeEmail"
                      name="employeeEmail"
                      type="email"
                      placeholder="Enter email address"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="jobRole">Job Title</Label>
                    <Select name="jobRole">
                      <SelectTrigger>
                        <SelectValue placeholder="Select job role" />
                      </SelectTrigger>
                      <SelectContent>
                        {jobRoles.map(role => (
                          <SelectItem key={role} value={role}>{role}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="department">Department</Label>
                    <Select name="department">
                      <SelectTrigger>
                        <SelectValue placeholder="Select department" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Engineering">Engineering</SelectItem>
                        <SelectItem value="Human Resources">Human Resources</SelectItem>
                        <SelectItem value="Sales">Sales</SelectItem>
                        <SelectItem value="Marketing">Marketing</SelectItem>
                        <SelectItem value="Finance">Finance</SelectItem>
                        <SelectItem value="Operations">Operations</SelectItem>
                        <SelectItem value="General">General</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="employmentType">Employment Type</Label>
                    <Select name="employmentType">
                      <SelectTrigger>
                        <SelectValue placeholder="Select employment type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="FullTime">Full-Time</SelectItem>
                        <SelectItem value="PartTime">Part-Time</SelectItem>
                        <SelectItem value="Contract">Contract</SelectItem>
                        <SelectItem value="Internship">Internship</SelectItem>
                        <SelectItem value="Consultant">Consultant</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="workLocation">Work Location</Label>
                    <Input
                      id="workLocation"
                      name="workLocation"
                      placeholder="Enter work location"
                    />
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Contact Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      placeholder="Enter phone number (e.g., +1234567890)"
                      pattern="[\+]?[1-9][\d]{0,15}"
                      title="Please enter a valid phone number"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="dateOfBirth">Date of Birth</Label>
                    <Input
                      id="dateOfBirth"
                      name="dateOfBirth"
                      type="date"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    name="address"
                    placeholder="Enter address"
                  />
                </div>
              </div>

              {/* Compensation */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Compensation</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="salary">Base Salary</Label>
                    <Input
                      id="salary"
                      name="salary"
                      type="number"
                      min="0"
                      step="0.01"
                      placeholder="Enter base salary"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="annualCTC">Annual CTC</Label>
                    <Input
                      id="annualCTC"
                      name="annualCTC"
                      type="number"
                      min="0"
                      step="0.01"
                      placeholder="Enter annual CTC"
                    />
                  </div>
                </div>
              </div>

              {/* Emergency Contact */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Emergency Contact</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="emergencyContactName">Contact Name</Label>
                    <Input
                      id="emergencyContactName"
                      name="emergencyContactName"
                      placeholder="Enter contact name"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="emergencyContactPhone">Contact Phone</Label>
                    <Input
                      id="emergencyContactPhone"
                      name="emergencyContactPhone"
                      placeholder="Enter contact phone"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="emergencyContactRelation">Relationship</Label>
                    <Input
                      id="emergencyContactRelation"
                      name="emergencyContactRelation"
                      placeholder="Enter relationship"
                    />
                  </div>
                </div>
              </div>

              {/* Additional Personal Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Additional Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="gender">Gender</Label>
                    <Select name="gender">
                      <SelectTrigger>
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Male">Male</SelectItem>
                        <SelectItem value="Female">Female</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                        <SelectItem value="PreferNotToSay">Prefer not to say</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="maritalStatus">Marital Status</Label>
                    <Select name="maritalStatus">
                      <SelectTrigger>
                        <SelectValue placeholder="Select marital status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Single">Single</SelectItem>
                        <SelectItem value="Married">Married</SelectItem>
                        <SelectItem value="Divorced">Divorced</SelectItem>
                        <SelectItem value="Widowed">Widowed</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bloodGroup">Blood Group</Label>
                    <Select name="bloodGroup">
                      <SelectTrigger>
                        <SelectValue placeholder="Select blood group" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="A+">A+</SelectItem>
                        <SelectItem value="A-">A-</SelectItem>
                        <SelectItem value="B+">B+</SelectItem>
                        <SelectItem value="B-">B-</SelectItem>
                        <SelectItem value="AB+">AB+</SelectItem>
                        <SelectItem value="AB-">AB-</SelectItem>
                        <SelectItem value="O+">O+</SelectItem>
                        <SelectItem value="O-">O-</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="nationality">Nationality</Label>
                  <Input
                    id="nationality"
                    name="nationality"
                    placeholder="Enter nationality"
                  />
                </div>
              </div>

              {/* Banking Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Banking Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="bankName">Bank Name</Label>
                    <Input
                      id="bankName"
                      name="bankName"
                      placeholder="Enter bank name"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bankAccountNumber">Account Number</Label>
                    <Input
                      id="bankAccountNumber"
                      name="bankAccountNumber"
                      placeholder="Enter account number"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bankIFSC">IFSC Code</Label>
                    <Input
                      id="bankIFSC"
                      name="bankIFSC"
                      placeholder="Enter IFSC code (e.g., ABCD0123456)"
                      pattern="[A-Z]{4}0[A-Z0-9]{6}"
                      title="IFSC should be in format: ABCD0123456"
                      maxLength={11}
                      style={{ textTransform: 'uppercase' }}
                    />
                  </div>
                </div>
              </div>

              {/* Government IDs */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Government IDs</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="pan">PAN Number</Label>
                    <Input
                      id="pan"
                      name="pan"
                      placeholder="Enter PAN number (e.g., **********)"
                      pattern="[A-Z]{5}[0-9]{4}[A-Z]{1}"
                      title="PAN should be in format: **********"
                      maxLength={10}
                      style={{ textTransform: 'uppercase' }}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="aadhar">Aadhar Number</Label>
                    <Input
                      id="aadhar"
                      name="aadhar"
                      placeholder="Enter Aadhar number (12 digits)"
                      pattern="[0-9]{12}"
                      title="Aadhar should be 12 digits"
                      maxLength={12}
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowAddEmployee(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">
                  Add Employee
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>

        {/* Employee Details Dialog */}
        <Dialog open={showEmployeeDetails} onOpenChange={setShowEmployeeDetails}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Employee Details</DialogTitle>
              <DialogDescription>
                Complete information for {selectedEmployee?.name}
              </DialogDescription>
            </DialogHeader>

            {selectedEmployee && (
              <div className="space-y-6">
                {/* Personal Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Personal Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center space-x-4">
                        <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white text-xl font-semibold">
                          {selectedEmployee.name?.split(' ').map((n: string) => n[0]).join('')}
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold">{selectedEmployee.name}</h3>
                          <p className="text-gray-600">{selectedEmployee.email}</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Employee ID</Label>
                          <p className="text-sm">{selectedEmployee.employeeDetails?.employeeId || 'N/A'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Phone</Label>
                          <p className="text-sm">{selectedEmployee.employeeDetails?.phone || 'N/A'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Date of Birth</Label>
                          <p className="text-sm">
                            {selectedEmployee.employeeDetails?.dateOfBirth
                              ? new Date(selectedEmployee.employeeDetails.dateOfBirth).toLocaleDateString()
                              : 'N/A'}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Gender</Label>
                          <p className="text-sm">{selectedEmployee.employeeDetails?.gender || 'N/A'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Marital Status</Label>
                          <p className="text-sm">{selectedEmployee.employeeDetails?.maritalStatus || 'N/A'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Blood Group</Label>
                          <p className="text-sm">{selectedEmployee.employeeDetails?.bloodGroup || 'N/A'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Nationality</Label>
                          <p className="text-sm">{selectedEmployee.employeeDetails?.nationality || 'N/A'}</p>
                        </div>
                      </div>

                      <div>
                        <Label className="text-sm font-medium text-gray-500">Address</Label>
                        <p className="text-sm">{selectedEmployee.employeeDetails?.address || 'N/A'}</p>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Employment Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Employment Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Job Title</Label>
                          <p className="text-sm font-medium">{selectedEmployee.employeeDetails?.jobTitle || 'N/A'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Department</Label>
                          <Badge variant="outline">{selectedEmployee.employeeDetails?.department || 'General'}</Badge>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Employment Type</Label>
                          <Badge className={getLevelColor(selectedEmployee.employeeDetails?.employmentType || 'FullTime')}>
                            {formatEmploymentType(selectedEmployee.employeeDetails?.employmentType || 'FullTime')}
                          </Badge>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Employment Status</Label>
                          <Badge variant={selectedEmployee.isActive ? "default" : "secondary"}>
                            {formatEmploymentStatus(selectedEmployee.employeeDetails?.employmentStatus || 'Active')}
                          </Badge>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Join Date</Label>
                          <p className="text-sm">
                            {selectedEmployee.employeeDetails?.joinDate
                              ? new Date(selectedEmployee.employeeDetails.joinDate).toLocaleDateString()
                              : 'N/A'}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Work Location</Label>
                          <p className="text-sm">{selectedEmployee.employeeDetails?.workLocation || 'N/A'}</p>
                        </div>
                      </div>

                      <div>
                        <Label className="text-sm font-medium text-gray-500">Total Experience</Label>
                        <p className="text-sm">{selectedEmployee.employeeDetails?.totalExperience || 'N/A'}</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Compensation & Emergency Contact */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Compensation</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Base Salary</Label>
                          <p className="text-sm font-medium">
                            {selectedEmployee.employeeDetails?.baseSalary
                              ? `$${selectedEmployee.employeeDetails.baseSalary.toLocaleString()}`
                              : 'N/A'}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Annual CTC</Label>
                          <p className="text-sm font-medium">
                            {selectedEmployee.employeeDetails?.annualCTC
                              ? `$${selectedEmployee.employeeDetails.annualCTC.toLocaleString()}`
                              : 'N/A'}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Next Salary Review</Label>
                          <p className="text-sm">
                            {selectedEmployee.employeeDetails?.nextSalaryReview
                              ? new Date(selectedEmployee.employeeDetails.nextSalaryReview).toLocaleDateString()
                              : 'N/A'}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">Performance Rating</Label>
                          <p className="text-sm">
                            {selectedEmployee.employeeDetails?.performanceRating
                              ? `${selectedEmployee.employeeDetails.performanceRating}/5.0`
                              : 'N/A'}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Emergency Contact</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label className="text-sm font-medium text-gray-500">Contact Name</Label>
                        <p className="text-sm">{selectedEmployee.employeeDetails?.emergencyContactName || 'N/A'}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-500">Contact Phone</Label>
                        <p className="text-sm">{selectedEmployee.employeeDetails?.emergencyContactPhone || 'N/A'}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-500">Relationship</Label>
                        <p className="text-sm">{selectedEmployee.employeeDetails?.emergencyContactRelation || 'N/A'}</p>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Login Credentials Section */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center space-x-2">
                        <Key className="h-5 w-5 text-blue-600" />
                        <span>Login Credentials</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {selectedEmployee.employeeDetails?.loginUsername ? (
                        <>
                          <div className="grid grid-cols-1 gap-4">
                            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                              <div className="flex items-center justify-between mb-2">
                                <Label className="text-sm font-medium text-blue-800">Login Username/Email</Label>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    navigator.clipboard.writeText(selectedEmployee.employeeDetails?.loginUsername || '');
                                    toast.success('Username copied to clipboard');
                                  }}
                                  className="h-8 px-2"
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                              </div>
                              <p className="text-sm font-mono bg-white px-3 py-2 rounded border">
                                {selectedEmployee.employeeDetails.loginUsername}
                              </p>
                            </div>

                            {(selectedEmployee.loginTemporaryPassword || selectedEmployee.employeeDetails?.loginTemporaryPassword) && (
                              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                                <div className="flex items-center justify-between mb-2">
                                  <Label className="text-sm font-medium text-yellow-800">Temporary Password</Label>
                                  <div className="flex items-center space-x-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => setShowEmployeePassword(!showEmployeePassword)}
                                      className="h-8 px-2"
                                      title={showEmployeePassword ? "Hide password" : "Show password"}
                                    >
                                      {showEmployeePassword ? (
                                        <EyeOff className="h-3 w-3" />
                                      ) : (
                                        <Eye className="h-3 w-3" />
                                      )}
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => {
                                        const password = selectedEmployee.loginTemporaryPassword || selectedEmployee.employeeDetails?.loginTemporaryPassword || '';
                                        navigator.clipboard.writeText(password);
                                        toast.success('Password copied to clipboard');
                                      }}
                                      className="h-8 px-2"
                                      title="Copy password"
                                    >
                                      <Copy className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </div>
                                <div className="relative">
                                  <p className="text-sm font-mono bg-white px-3 py-2 rounded border">
                                    {showEmployeePassword
                                      ? (selectedEmployee.loginTemporaryPassword || selectedEmployee.employeeDetails?.loginTemporaryPassword)
                                      : '••••••••••••'
                                    }
                                  </p>
                                </div>
                                <div className="flex items-center mt-2 text-xs text-yellow-700">
                                  <Shield className="h-3 w-3 mr-1" />
                                  <span>Password reset required on first login</span>
                                </div>
                              </div>
                            )}

                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label className="text-sm font-medium text-gray-500">Password Reset Required</Label>
                                <p className="text-sm">
                                  {selectedEmployee.employeeDetails.requirePasswordReset ? (
                                    <Badge variant="outline" className="bg-yellow-50 text-yellow-800 border-yellow-200">
                                      Yes
                                    </Badge>
                                  ) : (
                                    <Badge variant="outline" className="bg-green-50 text-green-800 border-green-200">
                                      No
                                    </Badge>
                                  )}
                                </p>
                              </div>
                              <div>
                                <Label className="text-sm font-medium text-gray-500">Credentials Generated</Label>
                                <p className="text-sm">
                                  {selectedEmployee.employeeDetails.passwordGeneratedAt
                                    ? new Date(selectedEmployee.employeeDetails.passwordGeneratedAt).toLocaleDateString()
                                    : 'N/A'}
                                </p>
                              </div>
                            </div>
                          </div>

                          <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                            <p className="text-xs text-gray-600">
                              <strong>Note:</strong> Share these credentials securely with the employee.
                              The employee will be required to change the password on first login.
                            </p>
                          </div>
                        </>
                      ) : (
                        <div className="text-center py-6">
                          <Key className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                          <p className="text-sm text-gray-500 mb-2">No login credentials generated for this employee</p>
                          <p className="text-xs text-gray-400 mb-4">
                            Login credentials are automatically generated when creating new employees
                          </p>
                          <div className="flex space-x-2">
                            <Button
                              onClick={() => setShowGenerateCredentialsModal(true)}
                              disabled={generatingCredentials}
                              className="bg-blue-600 hover:bg-blue-700 text-white"
                            >
                              <Key className="h-4 w-4 mr-2" />
                              Generate Credentials
                            </Button>
                            {(selectedEmployee?.loginUsername || selectedEmployee?.employeeDetails?.loginUsername) && (
                              <Button
                                onClick={() => setShowChangePasswordModal(true)}
                                disabled={changingPassword}
                                variant="outline"
                                className="border-orange-500 text-orange-600 hover:bg-orange-50"
                              >
                                <Key className="h-4 w-4 mr-2" />
                                Change Password
                              </Button>
                            )}
                          </div>
                          <p className="text-xs text-gray-400 mt-2">
                            This will create secure login credentials for the employee
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>

                {/* Additional Information */}
                {(selectedEmployee.employeeDetails?.bankAccountNumber ||
                  selectedEmployee.employeeDetails?.bankName ||
                  selectedEmployee.employeeDetails?.bankIFSC ||
                  selectedEmployee.employeeDetails?.pan ||
                  selectedEmployee.employeeDetails?.aadhar) && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Additional Information</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {selectedEmployee.employeeDetails?.bankAccountNumber && (
                          <div>
                            <Label className="text-sm font-medium text-gray-500">Bank Account</Label>
                            <p className="text-sm">{selectedEmployee.employeeDetails.bankAccountNumber}</p>
                            <p className="text-xs text-gray-400">
                              {selectedEmployee.employeeDetails?.bankName} - {selectedEmployee.employeeDetails?.bankIFSC}
                            </p>
                          </div>
                        )}
                        {selectedEmployee.employeeDetails?.pan && (
                          <div>
                            <Label className="text-sm font-medium text-gray-500">PAN</Label>
                            <p className="text-sm">{selectedEmployee.employeeDetails.pan}</p>
                          </div>
                        )}
                        {selectedEmployee.employeeDetails?.aadhar && (
                          <div>
                            <Label className="text-sm font-medium text-gray-500">Aadhar</Label>
                            <p className="text-sm">{selectedEmployee.employeeDetails.aadhar}</p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3 pt-6 border-t">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowEmployeeDetails(false);
                    handleViewEmployeeDashboard(selectedEmployee);
                  }}
                  className="flex items-center space-x-2"
                >
                  <BarChart3 className="h-4 w-4" />
                  <span>View Dashboard</span>
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowEmployeeDetails(false);
                    setShowEditEmployee(true);
                  }}
                  className="flex items-center space-x-2"
                >
                  <Edit className="h-4 w-4" />
                  <span>Edit Employee</span>
                </Button>
              </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Employee Edit Dialog */}
        <Dialog open={showEditEmployee} onOpenChange={setShowEditEmployee}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Employee</DialogTitle>
              <DialogDescription>
                Update information for {selectedEmployee?.name}
              </DialogDescription>
            </DialogHeader>

            {selectedEmployee && (
              <form onSubmit={handleEditEmployee} className="space-y-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Basic Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="editEmployeeName">Full Name</Label>
                      <Input
                        id="editEmployeeName"
                        name="employeeName"
                        defaultValue={selectedEmployee.name}
                        placeholder="Enter employee name"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="editEmployeeEmail">Email</Label>
                      <Input
                        id="editEmployeeEmail"
                        name="employeeEmail"
                        type="email"
                        defaultValue={selectedEmployee.email}
                        placeholder="Enter email address"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="editJobRole">Job Title</Label>
                      <Select name="jobRole" defaultValue={selectedEmployee.employeeDetails?.jobTitle}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select job role" />
                        </SelectTrigger>
                        <SelectContent>
                          {jobRoles.map(role => (
                            <SelectItem key={role} value={role}>{role}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="editDepartment">Department</Label>
                      <Select name="department" defaultValue={selectedEmployee.employeeDetails?.department}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select department" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Engineering">Engineering</SelectItem>
                          <SelectItem value="Human Resources">Human Resources</SelectItem>
                          <SelectItem value="Sales">Sales</SelectItem>
                          <SelectItem value="Marketing">Marketing</SelectItem>
                          <SelectItem value="Finance">Finance</SelectItem>
                          <SelectItem value="Operations">Operations</SelectItem>
                          <SelectItem value="General">General</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="editEmploymentType">Employment Type</Label>
                      <Select name="employmentType" defaultValue={selectedEmployee.employeeDetails?.employmentType}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select employment type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="FullTime">Full-Time</SelectItem>
                          <SelectItem value="PartTime">Part-Time</SelectItem>
                          <SelectItem value="Contract">Contract</SelectItem>
                          <SelectItem value="Internship">Internship</SelectItem>
                          <SelectItem value="Consultant">Consultant</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="editWorkLocation">Work Location</Label>
                      <Input
                        id="editWorkLocation"
                        name="workLocation"
                        defaultValue={selectedEmployee.employeeDetails?.workLocation}
                        placeholder="Enter work location"
                      />
                    </div>
                  </div>
                </div>

                {/* Contact Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Contact Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="editPhone">Phone</Label>
                      <Input
                        id="editPhone"
                        name="phone"
                        type="tel"
                        defaultValue={selectedEmployee.employeeDetails?.phone}
                        placeholder="Enter phone number (e.g., +1234567890)"
                        pattern="[\+]?[1-9][\d]{0,15}"
                        title="Please enter a valid phone number"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="editDateOfBirth">Date of Birth</Label>
                      <Input
                        id="editDateOfBirth"
                        name="dateOfBirth"
                        type="date"
                        defaultValue={selectedEmployee.employeeDetails?.dateOfBirth ?
                          new Date(selectedEmployee.employeeDetails.dateOfBirth).toISOString().split('T')[0] : ''}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="editAddress">Address</Label>
                    <Input
                      id="editAddress"
                      name="address"
                      defaultValue={selectedEmployee.employeeDetails?.address}
                      placeholder="Enter address"
                    />
                  </div>
                </div>

                {/* Compensation */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Compensation</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="editSalary">Base Salary</Label>
                      <Input
                        id="editSalary"
                        name="salary"
                        type="number"
                        defaultValue={selectedEmployee.employeeDetails?.baseSalary}
                        placeholder="Enter base salary"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="editAnnualCTC">Annual CTC</Label>
                      <Input
                        id="editAnnualCTC"
                        name="annualCTC"
                        type="number"
                        defaultValue={selectedEmployee.employeeDetails?.annualCTC}
                        placeholder="Enter annual CTC"
                      />
                    </div>
                  </div>
                </div>

                {/* Emergency Contact */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Emergency Contact</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="editEmergencyContactName">Contact Name</Label>
                      <Input
                        id="editEmergencyContactName"
                        name="emergencyContactName"
                        defaultValue={selectedEmployee.employeeDetails?.emergencyContactName}
                        placeholder="Enter contact name"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="editEmergencyContactPhone">Contact Phone</Label>
                      <Input
                        id="editEmergencyContactPhone"
                        name="emergencyContactPhone"
                        defaultValue={selectedEmployee.employeeDetails?.emergencyContactPhone}
                        placeholder="Enter contact phone"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="editEmergencyContactRelation">Relationship</Label>
                      <Input
                        id="editEmergencyContactRelation"
                        name="emergencyContactRelation"
                        defaultValue={selectedEmployee.employeeDetails?.emergencyContactRelation}
                        placeholder="Enter relationship"
                      />
                    </div>
                  </div>
                </div>

                {/* Additional Personal Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Additional Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="editGender">Gender</Label>
                      <Select name="gender" defaultValue={selectedEmployee.employeeDetails?.gender}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select gender" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Male">Male</SelectItem>
                          <SelectItem value="Female">Female</SelectItem>
                          <SelectItem value="Other">Other</SelectItem>
                          <SelectItem value="PreferNotToSay">Prefer not to say</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="editMaritalStatus">Marital Status</Label>
                      <Select name="maritalStatus" defaultValue={selectedEmployee.employeeDetails?.maritalStatus}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select marital status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Single">Single</SelectItem>
                          <SelectItem value="Married">Married</SelectItem>
                          <SelectItem value="Divorced">Divorced</SelectItem>
                          <SelectItem value="Widowed">Widowed</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="editBloodGroup">Blood Group</Label>
                      <Select name="bloodGroup" defaultValue={selectedEmployee.employeeDetails?.bloodGroup}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select blood group" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="A+">A+</SelectItem>
                          <SelectItem value="A-">A-</SelectItem>
                          <SelectItem value="B+">B+</SelectItem>
                          <SelectItem value="B-">B-</SelectItem>
                          <SelectItem value="AB+">AB+</SelectItem>
                          <SelectItem value="AB-">AB-</SelectItem>
                          <SelectItem value="O+">O+</SelectItem>
                          <SelectItem value="O-">O-</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="editNationality">Nationality</Label>
                    <Input
                      id="editNationality"
                      name="nationality"
                      defaultValue={selectedEmployee.employeeDetails?.nationality}
                      placeholder="Enter nationality"
                    />
                  </div>
                </div>

                {/* Banking Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Banking Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="editBankName">Bank Name</Label>
                      <Input
                        id="editBankName"
                        name="bankName"
                        defaultValue={selectedEmployee.employeeDetails?.bankName}
                        placeholder="Enter bank name"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="editBankAccountNumber">Account Number</Label>
                      <Input
                        id="editBankAccountNumber"
                        name="bankAccountNumber"
                        defaultValue={selectedEmployee.employeeDetails?.bankAccountNumber}
                        placeholder="Enter account number"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="editBankIFSC">IFSC Code</Label>
                      <Input
                        id="editBankIFSC"
                        name="bankIFSC"
                        defaultValue={selectedEmployee.employeeDetails?.bankIFSC}
                        placeholder="Enter IFSC code"
                      />
                    </div>
                  </div>
                </div>

                {/* Government IDs */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Government IDs</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="editPan">PAN Number</Label>
                      <Input
                        id="editPan"
                        name="pan"
                        defaultValue={selectedEmployee.employeeDetails?.pan}
                        placeholder="Enter PAN number"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="editAadhar">Aadhar Number</Label>
                      <Input
                        id="editAadhar"
                        name="aadhar"
                        defaultValue={selectedEmployee.employeeDetails?.aadhar}
                        placeholder="Enter Aadhar number"
                      />
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowEditEmployee(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">
                    Update Employee
                  </Button>
                </div>
              </form>
            )}
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Delete Employee</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete {employeeToDelete?.name}? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                variant="outline"
                onClick={() => {
                  setShowDeleteConfirm(false);
                  setEmployeeToDelete(null);
                }}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={confirmDeleteEmployee}
              >
                Delete Employee
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* Assign Manager Dialog */}
        <Dialog open={showAssignManagerDialog} onOpenChange={setShowAssignManagerDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Assign Manager</DialogTitle>
              <DialogDescription>
                Select a manager for {selectedEmployeeForAssignment?.name}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <Label htmlFor="manager-select">Select Manager</Label>
                <Select
                  value={selectedManager?.id || ''}
                  onValueChange={(value) => {
                    const manager = hierarchyEmployees.find(emp => emp.id === value);
                    setSelectedManager(manager);
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a manager..." />
                  </SelectTrigger>
                  <SelectContent>
                    {hierarchyEmployees
                      .filter(emp =>
                        emp.id !== selectedEmployeeForAssignment?.id && // Can't be their own manager
                        user?.id !== emp.id && // Can't assign current user as manager to others
                        emp.isActive // Only active employees
                      )
                      .map((employee) => (
                        <SelectItem key={employee.id} value={employee.id}>
                          <div className="flex items-center space-x-2">
                            <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-semibold">
                              {employee.name.split(' ').map((n: string) => n[0]).join('')}
                            </div>
                            <div>
                              <span className="font-medium">{employee.name}</span>
                              <span className="text-sm text-gray-600 ml-2">
                                {employee.employeeDetails?.jobTitle}
                              </span>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedManager && (
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                      {selectedManager.name.split(' ').map((n: string) => n[0]).join('')}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{selectedManager.name}</div>
                      <div className="text-sm text-gray-600">
                        {selectedManager.employeeDetails?.jobTitle} • {selectedManager.employeeDetails?.department}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                variant="outline"
                onClick={() => {
                  setShowAssignManagerDialog(false);
                  setSelectedEmployeeForAssignment(null);
                  setSelectedManager(null);
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={confirmAssignManager}
                disabled={!selectedManager}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Assign Manager
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center mb-2">
              <Users className="h-8 w-8 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {loading ? <Loader2 className="h-6 w-6 animate-spin mx-auto" /> : employeeStats.totalEmployees || 0}
            </div>
            <div className="text-sm text-gray-600">Total Employees</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center mb-2">
              <Settings className="h-8 w-8 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {loading ? <Loader2 className="h-6 w-6 animate-spin mx-auto" /> : employeeStats.activeEmployees || 0}
            </div>
            <div className="text-sm text-gray-600">Active Employees</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center mb-2">
              <UserPlus className="h-8 w-8 text-purple-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {loading ? <Loader2 className="h-6 w-6 animate-spin mx-auto" /> : employeeStats.newHiresThisMonth || 0}
            </div>
            <div className="text-sm text-gray-600">New This Month</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center mb-2">
              <Eye className="h-8 w-8 text-orange-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {loading ? <Loader2 className="h-6 w-6 animate-spin mx-auto" /> :
                Object.keys(employeeStats.departmentBreakdown || {}).length}
            </div>
            <div className="text-sm text-gray-600">Departments</div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="employees">Employee List</TabsTrigger>
          <TabsTrigger value="hierarchy">Organization Tree</TabsTrigger>
          <TabsTrigger value="manage-hierarchy">Manage Hierarchy</TabsTrigger>
          <TabsTrigger value="permissions">Permissions</TabsTrigger>
        </TabsList>

        <TabsContent value="employees">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>All Employees</CardTitle>
                  <CardDescription>Manage and view all employees in your organization</CardDescription>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                </div>
              </div>

              {/* Bulk Actions */}
              {selectedEmployees.length > 0 && (
                <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg mt-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-blue-900">
                      {selectedEmployees.length} employee{selectedEmployees.length > 1 ? 's' : ''} selected
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedEmployees([])}
                    >
                      Clear Selection
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={handleBulkDelete}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Selected
                    </Button>
                  </div>
                </div>
              )}

              {/* Search and Filters */}
              <div className="flex flex-wrap gap-4 mt-4">
                <div className="flex-1 min-w-[200px]">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search employees..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={filterDepartment || "all"} onValueChange={(value) => setFilterDepartment(value === "all" ? "" : value)}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Department" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Departments</SelectItem>
                    <SelectItem value="Engineering">Engineering</SelectItem>
                    <SelectItem value="Human Resources">Human Resources</SelectItem>
                    <SelectItem value="Sales">Sales</SelectItem>
                    <SelectItem value="Marketing">Marketing</SelectItem>
                    <SelectItem value="Finance">Finance</SelectItem>
                    <SelectItem value="Operations">Operations</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={filterRole || "all"} onValueChange={(value) => setFilterRole(value === "all" ? "" : value)}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Roles</SelectItem>
                    <SelectItem value="employee">Employee</SelectItem>
                    <SelectItem value="orgadmin">Admin</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={filterEmploymentType || "all"} onValueChange={(value) => setFilterEmploymentType(value === "all" ? "" : value)}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="full-time">Full Time</SelectItem>
                    <SelectItem value="part-time">Part Time</SelectItem>
                    <SelectItem value="contract">Contract</SelectItem>
                    <SelectItem value="intern">Intern</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
                  <span className="ml-2 text-gray-500">Loading employees...</span>
                </div>
              ) : employees.length === 0 ? (
                <div className="text-center py-12">
                  <Users className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No employees found</h3>
                  <p className="text-gray-600 mb-4">Get started by adding your first employee to the organization.</p>
                  <Button
                    onClick={() => setShowAddEmployee(true)}
                    className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                  >
                    <UserPlus className="h-4 w-4 mr-2" />
                    Add First Employee
                  </Button>
                </div>
              ) : (
                <>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <input
                            type="checkbox"
                            checked={selectedEmployees.length === employees.length && employees.length > 0}
                            onChange={(e) => handleSelectAllEmployees(e.target.checked)}
                            className="rounded border-gray-300"
                          />
                        </TableHead>
                        <TableHead>Employee</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead>Department</TableHead>
                        <TableHead>Employment Type</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {employees.map((employee) => (
                        <TableRow key={employee.id}>
                          <TableCell>
                            <input
                              type="checkbox"
                              checked={selectedEmployees.includes(employee.id)}
                              onChange={(e) => handleSelectEmployee(employee.id, e.target.checked)}
                              className="rounded border-gray-300"
                            />
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                                {(employee?.name || 'N/A').split(' ').map((n: string) => n[0]).join('')}
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">{employee.name}</div>
                                <div className="text-sm text-gray-500">{employee.email}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div className="font-medium">{employee.employeeDetails?.jobTitle || employee.role}</div>
                              <div className="text-gray-500">ID: {employee.employeeDetails?.employeeId || 'N/A'}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-col space-y-1">
                              <Badge variant="outline">
                                {employee.employeeDetails?.department || 'General'}
                              </Badge>
                              {employee.employeeDetails?.workLocation && (
                                <span className="text-xs text-gray-500">
                                  📍 {employee.employeeDetails.workLocation}
                                </span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-col space-y-1">
                              <Badge className={getLevelColor(employee.employeeDetails?.employmentType || 'FullTime')}>
                                {formatEmploymentType(employee.employeeDetails?.employmentType || 'FullTime')}
                              </Badge>
                              {employee.employeeDetails?.employmentStatus && (
                                <span className="text-xs text-gray-500">
                                  Status: {formatEmploymentStatus(employee.employeeDetails.employmentStatus)}
                                </span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant={employee.isActive ? "default" : "secondary"}>
                              {employee.isActive ? 'Active' : 'Inactive'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleViewEmployee(employee)}
                                title="View employee details"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleViewEmployeeDashboard(employee)}
                                title="View employee dashboard"
                              >
                                <BarChart3 className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                disabled={user && employee.id === user.id}
                                onClick={() => {
                                  setSelectedEmployee(employee);
                                  setShowEditEmployee(true);
                                }}
                                title={user && employee.id === user.id ? "You cannot edit your own record" : "Edit employee"}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                disabled={user && employee.id === user.id}
                                className={`${user && employee.id === user.id ? 'opacity-50 cursor-not-allowed' : 'text-red-600 hover:text-red-700'}`}
                                onClick={() => handleDeleteEmployee(employee)}
                                title={user && employee.id === user.id ? "You cannot delete your own record" : "Delete employee"}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="flex justify-between items-center mt-4">
                      <div className="text-sm text-gray-500">
                        Page {currentPage} of {totalPages}
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                          disabled={currentPage === 1}
                        >
                          Previous
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                          disabled={currentPage === totalPages}
                        >
                          Next
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="hierarchy">
          <Card>
            <CardHeader>
              <CardTitle>Organization Hierarchy</CardTitle>
              <CardDescription>View your organization's structure and reporting relationships</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
                  <span className="ml-2 text-gray-500">Loading organization structure...</span>
                </div>
              ) : Object.keys(organizationStructure).length === 0 ? (
                <div className="text-center py-12">
                  <Settings className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No departments found</h3>
                  <p className="text-gray-600 mb-4">Add employees to create your organizational structure.</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Department-based Organization Tree */}
                  {Object.entries(organizationStructure).map(([deptKey, dept]: [string, any]) => (
                    <Card key={deptKey} className="border-l-4 border-l-blue-500">
                      <CardHeader
                        className="cursor-pointer hover:bg-gray-50"
                        onClick={() => toggleDepartment(deptKey)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            {expandedDepts.includes(deptKey) ?
                              <ChevronDown className="h-5 w-5 text-blue-600" /> :
                              <ChevronRight className="h-5 w-5 text-blue-600" />
                            }
                            <div className="flex items-center space-x-2">
                              <Building2 className="h-5 w-5 text-blue-600" />
                              <h3 className="text-xl font-semibold text-gray-900">{dept?.name || 'Unknown Department'}</h3>
                            </div>
                            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                              {(dept.employees || []).length} employees
                            </Badge>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium text-gray-700">Department Head</p>
                            <p className="text-sm text-gray-600">{dept?.manager || 'Not Assigned'}</p>
                          </div>
                        </div>
                      </CardHeader>

                      {expandedDepts.includes(deptKey) && (
                        <CardContent className="pt-0">
                          <div className="space-y-3">
                            {/* Group employees by manager */}
                            {(() => {
                              const employeesByManager = (dept?.employees || []).reduce((acc: any, emp: any) => {
                                const manager = emp.reportingTo || 'Direct Reports';
                                if (!acc[manager]) acc[manager] = [];
                                acc[manager].push(emp);
                                return acc;
                              }, {});

                              return Object.entries(employeesByManager).map(([manager, empList]: [string, any]) => (
                                <div key={manager} className="ml-4">
                                  {manager !== 'Direct Reports' && (
                                    <div className="flex items-center space-x-2 mb-2 text-sm font-medium text-gray-700">
                                      <User className="h-4 w-4" />
                                      <span>Team Lead: {manager}</span>
                                    </div>
                                  )}

                                  <div className="ml-6 space-y-2">
                                    {(empList as any[]).map((emp: any) => (
                                      <div key={emp.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border">
                                        <div className="flex items-center space-x-3">
                                          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                                            {(emp?.name || 'N/A').split(' ').map((n: string) => n[0]).join('')}
                                          </div>
                                          <div>
                                            <div className="flex items-center space-x-2">
                                              <span className="font-medium text-gray-900">{emp.name}</span>
                                              <Badge variant="outline" className="text-xs">
                                                {emp.email}
                                              </Badge>
                                            </div>
                                            <div className="flex items-center space-x-2 mt-1">
                                              <span className="text-sm text-gray-600">{emp.role}</span>
                                              <span className="text-xs text-gray-400">•</span>
                                              <Badge className={getLevelColor(emp.level)} size="sm">
                                                {formatEmploymentType(emp.level)}
                                              </Badge>
                                            </div>
                                          </div>
                                        </div>

                                        <div className="flex items-center space-x-2">
                                          <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => {
                                              // Find the full employee object from the employees list
                                              const fullEmployee = employees.find(e => e.id === emp.id);
                                              if (fullEmployee) {
                                                handleViewEmployee(fullEmployee);
                                              }
                                            }}
                                            title="View employee details"
                                          >
                                            <Eye className="h-4 w-4" />
                                          </Button>
                                          <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => {
                                              // Find the full employee object from the employees list
                                              const fullEmployee = employees.find(e => e.id === emp.id);
                                              if (fullEmployee) {
                                                handleViewEmployeeDashboard(fullEmployee);
                                              }
                                            }}
                                            title="View employee dashboard"
                                          >
                                            <BarChart3 className="h-4 w-4" />
                                          </Button>
                                          <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => {
                                              const fullEmployee = employees.find(e => e.id === emp.id);
                                              if (fullEmployee) {
                                                setSelectedEmployee(fullEmployee);
                                                setShowEditEmployee(true);
                                              }
                                            }}
                                          >
                                            <Edit className="h-4 w-4" />
                                          </Button>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              ));
                            })()}
                          </div>
                        </CardContent>
                      )}
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="manage-hierarchy">
          <Card>
            <CardHeader>
              <CardTitle>Manage Hierarchy</CardTitle>
              <CardDescription>Assign and manage reporting relationships between employees</CardDescription>
            </CardHeader>
            <CardContent>
              {hierarchyLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
                  <span className="ml-2 text-gray-500">Loading hierarchy management...</span>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Instructions */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start space-x-3">
                      <Settings className="h-5 w-5 text-blue-600 mt-0.5" />
                      <div>
                        <h4 className="text-sm font-medium text-blue-900">How to manage hierarchy:</h4>
                        <ul className="text-sm text-blue-700 mt-1 space-y-1">
                          <li>• Click "Assign Manager" to assign an employee to a manager</li>
                          <li>• Drag and drop employees onto managers to reassign them</li>
                          <li>• Click "Remove Manager" to remove reporting relationships</li>
                          <li>• Circular reporting relationships are automatically prevented</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  {/* Hierarchy Management Interface */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Employees without managers */}
                    <Card className="border-l-4 border-l-orange-500">
                      <CardHeader>
                        <CardTitle className="text-lg flex items-center space-x-2">
                          <Users className="h-5 w-5 text-orange-600" />
                          <span>Employees without Managers</span>
                        </CardTitle>
                        <CardDescription>
                          These employees don't have assigned managers
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3 max-h-96 overflow-y-auto">
                          {hierarchyEmployees
                            .filter(emp => !emp.employeeDetails?.managerId && user?.id !== emp.id)
                            .map((employee) => (
                              <div
                                key={employee.id}
                                draggable
                                onDragStart={(e) => handleDragStart(e, employee)}
                                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border hover:bg-gray-100 cursor-move transition-colors"
                              >
                                <div className="flex items-center space-x-3">
                                  <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                                    {employee.name.split(' ').map((n: string) => n[0]).join('')}
                                  </div>
                                  <div>
                                    <div className="font-medium text-gray-900">{employee.name}</div>
                                    <div className="text-sm text-gray-600">
                                      {employee.employeeDetails?.jobTitle} • {employee.employeeDetails?.department}
                                    </div>
                                  </div>
                                </div>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleAssignManager(employee)}
                                  className="text-blue-600 hover:text-blue-700"
                                >
                                  Assign Manager
                                </Button>
                              </div>
                            ))}
                          {hierarchyEmployees.filter(emp => !emp.employeeDetails?.managerId && user?.id !== emp.id).length === 0 && (
                            <div className="text-center py-8 text-gray-500">
                              <Users className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                              <p>All employees have assigned managers</p>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>

                    {/* Managers and their teams */}
                    <Card className="border-l-4 border-l-green-500">
                      <CardHeader>
                        <CardTitle className="text-lg flex items-center space-x-2">
                          <Building2 className="h-5 w-5 text-green-600" />
                          <span>Managers and Teams</span>
                        </CardTitle>
                        <CardDescription>
                          Drop employees here to assign them to managers
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4 max-h-96 overflow-y-auto">
                          {/* Get unique managers */}
                          {Array.from(new Set(
                            hierarchyEmployees
                              .filter(emp => emp.employeeDetails?.managerId)
                              .map(emp => emp.employeeDetails?.managerId)
                          )).map((managerId) => {
                            const manager = hierarchyEmployees.find(emp => emp.id === managerId);
                            const subordinates = hierarchyEmployees.filter(emp => emp.employeeDetails?.managerId === managerId);

                            if (!manager) return null;

                            return (
                              <div
                                key={managerId}
                                onDragOver={handleDragOver}
                                onDrop={(e) => handleDrop(e, manager)}
                                className="border rounded-lg p-4 bg-green-50 hover:bg-green-100 transition-colors"
                              >
                                {/* Manager */}
                                <div className="flex items-center justify-between mb-3 pb-3 border-b border-green-200">
                                  <div className="flex items-center space-x-3">
                                    <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                                      {manager.name.split(' ').map((n: string) => n[0]).join('')}
                                    </div>
                                    <div>
                                      <div className="font-medium text-gray-900">{manager.name}</div>
                                      <div className="text-sm text-gray-600">
                                        {manager.employeeDetails?.jobTitle} • Manager
                                      </div>
                                    </div>
                                  </div>
                                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                                    {subordinates.length} direct report{subordinates.length !== 1 ? 's' : ''}
                                  </Badge>
                                </div>

                                {/* Subordinates */}
                                <div className="space-y-2 ml-4">
                                  {subordinates.map((subordinate) => (
                                    <div
                                      key={subordinate.id}
                                      draggable
                                      onDragStart={(e) => handleDragStart(e, subordinate)}
                                      className="flex items-center justify-between p-2 bg-white rounded border hover:bg-gray-50 cursor-move transition-colors"
                                    >
                                      <div className="flex items-center space-x-2">
                                        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-semibold">
                                          {subordinate.name.split(' ').map((n: string) => n[0]).join('')}
                                        </div>
                                        <div>
                                          <div className="text-sm font-medium text-gray-900">{subordinate.name}</div>
                                          <div className="text-xs text-gray-600">{subordinate.employeeDetails?.jobTitle}</div>
                                        </div>
                                      </div>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleRemoveManager(subordinate)}
                                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                      >
                                        Remove
                                      </Button>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            );
                          })}

                          {/* Potential managers (employees who could be managers) */}
                          {hierarchyEmployees
                            .filter(emp =>
                              !hierarchyEmployees.some(e => e.employeeDetails?.managerId === emp.id) && // Not currently a manager
                              emp.employeeDetails?.managerId && // Has a manager (not top-level)
                              user?.id !== emp.id // Not current user
                            ).length > 0 && (
                            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 bg-gray-50">
                              <h4 className="text-sm font-medium text-gray-700 mb-2">Potential Managers</h4>
                              <p className="text-xs text-gray-600 mb-3">These employees could become managers by having others report to them</p>
                              <div className="space-y-2">
                                {hierarchyEmployees
                                  .filter(emp =>
                                    !hierarchyEmployees.some(e => e.employeeDetails?.managerId === emp.id) &&
                                    emp.employeeDetails?.managerId &&
                                    user?.id !== emp.id
                                  )
                                  .map((employee) => (
                                    <div
                                      key={employee.id}
                                      onDragOver={handleDragOver}
                                      onDrop={(e) => handleDrop(e, employee)}
                                      className="flex items-center space-x-2 p-2 bg-white rounded border hover:bg-blue-50 transition-colors"
                                    >
                                      <div className="w-8 h-8 bg-gradient-to-r from-gray-500 to-gray-600 rounded-full flex items-center justify-center text-white text-xs font-semibold">
                                        {employee.name.split(' ').map((n: string) => n[0]).join('')}
                                      </div>
                                      <div>
                                        <div className="text-sm font-medium text-gray-900">{employee.name}</div>
                                        <div className="text-xs text-gray-600">{employee.employeeDetails?.jobTitle}</div>
                                      </div>
                                    </div>
                                  ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="permissions">
          <Card>
            <CardHeader>
              <CardTitle>Employee Permissions</CardTitle>
              <CardDescription>Manage role-based permissions for your employees</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {permissions.map(permission => (
                    <div key={permission.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold">{permission.name}</h4>
                        <Badge variant="outline">Role-based</Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{permission.description}</p>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500">Available to:</span>
                        <Badge variant="secondary" className="text-xs">Managers</Badge>
                        <Badge variant="secondary" className="text-xs">Admins</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Generate Credentials Modal */}
      <Dialog open={showGenerateCredentialsModal} onOpenChange={setShowGenerateCredentialsModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Key className="h-5 w-5 text-blue-600" />
              Generate Login Credentials
            </DialogTitle>
            <DialogDescription>
              Generate secure login credentials for {selectedEmployee?.name}. The employee will use their email address ({selectedEmployee?.email}) as the username.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Password Option Toggle */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="useCustomPassword"
                checked={useCustomPassword}
                onChange={(e) => {
                  setUseCustomPassword(e.target.checked);
                  if (!e.target.checked) {
                    setCustomPassword('');
                    setShowPassword(false);
                  }
                }}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <Label htmlFor="useCustomPassword" className="text-sm font-medium">
                Set custom password (otherwise auto-generate secure password)
              </Label>
            </div>

            {/* Custom Password Input */}
            {useCustomPassword && (
              <div className="space-y-3">
                <div className="space-y-2">
                  <Label htmlFor="customPassword" className="text-sm font-medium">
                    Custom Password
                  </Label>
                  <div className="relative">
                    <Input
                      id="customPassword"
                      type={showPassword ? 'text' : 'password'}
                      value={customPassword}
                      onChange={(e) => setCustomPassword(e.target.value)}
                      placeholder="Enter custom password"
                      className="pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4 text-gray-400" />
                      ) : (
                        <Eye className="h-4 w-4 text-gray-400" />
                      )}
                    </Button>
                  </div>
                </div>

                {/* Password Strength Indicator */}
                {customPassword && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-600">Password Strength</span>
                      <span className={`font-medium ${
                        passwordStrength.score >= 75 ? 'text-green-600' :
                        passwordStrength.score >= 60 ? 'text-yellow-600' :
                        passwordStrength.score >= 40 ? 'text-orange-600' :
                        'text-red-600'
                      }`}>
                        {passwordStrength.strength}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${passwordStrength.color}`}
                        style={{ width: `${passwordStrength.score}%` }}
                      />
                    </div>
                  </div>
                )}

                {/* Password Requirements */}
                <div className="text-xs text-gray-600 space-y-1">
                  <p className="font-medium">Password must contain:</p>
                  <ul className="space-y-1 ml-2">
                    <li className={`flex items-center gap-1 ${customPassword.length >= 8 ? 'text-green-600' : 'text-gray-400'}`}>
                      {customPassword.length >= 8 ? <CheckCircle className="h-3 w-3" /> : <AlertCircle className="h-3 w-3" />}
                      At least 8 characters
                    </li>
                    <li className={`flex items-center gap-1 ${/[A-Z]/.test(customPassword) ? 'text-green-600' : 'text-gray-400'}`}>
                      {/[A-Z]/.test(customPassword) ? <CheckCircle className="h-3 w-3" /> : <AlertCircle className="h-3 w-3" />}
                      One uppercase letter
                    </li>
                    <li className={`flex items-center gap-1 ${/[a-z]/.test(customPassword) ? 'text-green-600' : 'text-gray-400'}`}>
                      {/[a-z]/.test(customPassword) ? <CheckCircle className="h-3 w-3" /> : <AlertCircle className="h-3 w-3" />}
                      One lowercase letter
                    </li>
                    <li className={`flex items-center gap-1 ${/\d/.test(customPassword) ? 'text-green-600' : 'text-gray-400'}`}>
                      {/\d/.test(customPassword) ? <CheckCircle className="h-3 w-3" /> : <AlertCircle className="h-3 w-3" />}
                      One number
                    </li>
                    <li className={`flex items-center gap-1 ${/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(customPassword) ? 'text-green-600' : 'text-gray-400'}`}>
                      {/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(customPassword) ? <CheckCircle className="h-3 w-3" /> : <AlertCircle className="h-3 w-3" />}
                      One special character
                    </li>
                  </ul>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-end space-x-2 pt-4">
              <Button
                variant="outline"
                onClick={() => {
                  setShowGenerateCredentialsModal(false);
                  setCustomPassword('');
                  setUseCustomPassword(false);
                  setShowPassword(false);
                }}
                disabled={generatingCredentials}
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  const password = useCustomPassword ? customPassword : undefined;
                  if (useCustomPassword && (!customPassword || passwordStrength.score < 40)) {
                    toast.error('Please enter a stronger password');
                    return;
                  }
                  handleGenerateCredentials(selectedEmployee.id, password);
                }}
                disabled={generatingCredentials || (useCustomPassword && (!customPassword || passwordStrength.score < 40))}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                {generatingCredentials ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Key className="h-4 w-4 mr-2" />
                    Generate Credentials
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Change Password Modal */}
      <Dialog open={showChangePasswordModal} onOpenChange={setShowChangePasswordModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Key className="h-5 w-5 text-orange-600" />
              Change Employee Password
            </DialogTitle>
            <DialogDescription>
              Change the password for {selectedEmployee?.name}. The employee will be required to reset this password on their next login.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Current Credentials Info */}
            <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
              <div className="text-sm">
                <p className="font-medium text-gray-700">Current Login Details:</p>
                <p className="text-gray-600">Username: {selectedEmployee?.email}</p>
                <p className="text-gray-600">Current Password: {(selectedEmployee?.loginTemporaryPassword || selectedEmployee?.employeeDetails?.loginTemporaryPassword) ? '••••••••••••' : 'Not set'}</p>
              </div>
            </div>

            {/* New Password Input */}
            <div className="space-y-3">
              <div className="space-y-2">
                <Label htmlFor="newPassword" className="text-sm font-medium">
                  New Password
                </Label>
                <div className="relative">
                  <Input
                    id="newPassword"
                    type={showNewPassword ? 'text' : 'password'}
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    placeholder="Enter new password"
                    className="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                  >
                    {showNewPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </Button>
                </div>
              </div>

              {/* Password Strength Indicator */}
              {newPassword && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-600">Password Strength</span>
                    <span className={`font-medium ${
                      newPasswordStrength.score >= 75 ? 'text-green-600' :
                      newPasswordStrength.score >= 60 ? 'text-yellow-600' :
                      newPasswordStrength.score >= 40 ? 'text-orange-600' :
                      'text-red-600'
                    }`}>
                      {newPasswordStrength.strength}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${newPasswordStrength.color}`}
                      style={{ width: `${newPasswordStrength.score}%` }}
                    />
                  </div>
                </div>
              )}

              {/* Password Requirements */}
              <div className="text-xs text-gray-600 space-y-1">
                <p className="font-medium">Password must contain:</p>
                <ul className="space-y-1 ml-2">
                  <li className={`flex items-center gap-1 ${newPassword.length >= 8 ? 'text-green-600' : 'text-gray-400'}`}>
                    {newPassword.length >= 8 ? <CheckCircle className="h-3 w-3" /> : <AlertCircle className="h-3 w-3" />}
                    At least 8 characters
                  </li>
                  <li className={`flex items-center gap-1 ${/[A-Z]/.test(newPassword) ? 'text-green-600' : 'text-gray-400'}`}>
                    {/[A-Z]/.test(newPassword) ? <CheckCircle className="h-3 w-3" /> : <AlertCircle className="h-3 w-3" />}
                    One uppercase letter
                  </li>
                  <li className={`flex items-center gap-1 ${/[a-z]/.test(newPassword) ? 'text-green-600' : 'text-gray-400'}`}>
                    {/[a-z]/.test(newPassword) ? <CheckCircle className="h-3 w-3" /> : <AlertCircle className="h-3 w-3" />}
                    One lowercase letter
                  </li>
                  <li className={`flex items-center gap-1 ${/\d/.test(newPassword) ? 'text-green-600' : 'text-gray-400'}`}>
                    {/\d/.test(newPassword) ? <CheckCircle className="h-3 w-3" /> : <AlertCircle className="h-3 w-3" />}
                    One number
                  </li>
                  <li className={`flex items-center gap-1 ${/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(newPassword) ? 'text-green-600' : 'text-gray-400'}`}>
                    {/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(newPassword) ? <CheckCircle className="h-3 w-3" /> : <AlertCircle className="h-3 w-3" />}
                    One special character
                  </li>
                </ul>
              </div>
            </div>

            {/* Security Notice */}
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start space-x-2">
                <Shield className="h-4 w-4 text-yellow-600 mt-0.5" />
                <div className="text-xs text-yellow-700">
                  <p className="font-medium">Security Notice:</p>
                  <p>The employee will be required to change this password on their next login for security.</p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-2 pt-4">
              <Button
                variant="outline"
                onClick={() => {
                  setShowChangePasswordModal(false);
                  setNewPassword('');
                  setShowNewPassword(false);
                }}
                disabled={changingPassword}
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  if (!newPassword || newPasswordStrength.score < 40) {
                    toast.error('Please enter a stronger password');
                    return;
                  }
                  handleChangePassword(selectedEmployee.id, newPassword);
                }}
                disabled={changingPassword || !newPassword || newPasswordStrength.score < 40}
                className="bg-orange-600 hover:bg-orange-700 text-white"
              >
                {changingPassword ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Changing...
                  </>
                ) : (
                  <>
                    <Key className="h-4 w-4 mr-2" />
                    Change Password
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EmployeeManagement;
