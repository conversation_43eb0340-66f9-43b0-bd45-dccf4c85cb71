import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Calendar as CalendarIcon,
  Clock,
  Plus,
  FileText,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader2,
  Eye,
  Info
} from 'lucide-react';
import { format } from 'date-fns';
import { toast } from 'sonner';
import apiService from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';

interface LeaveBalance {
  leaveType: string;
  totalAllocated: number;
  usedLeaves: number;
  remainingLeaves: number;
  carriedForward: number;
}

interface LeaveRequest {
  id: string;
  leaveType: string;
  fromDate: string;
  toDate: string;
  totalDays: number;
  status: string;
  reason: string;
  appliedDate: string;
  approvedBy?: string;
  approvedAt?: string;
  comments?: string;
}

const EmployeeLeaveManagement: React.FC = () => {
  const { user } = useAuth();
  const [leaveBalances, setLeaveBalances] = useState<LeaveBalance[]>([]);
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  // Form state
  const [showApplyForm, setShowApplyForm] = useState(false);
  const [fromDate, setFromDate] = useState<Date>();
  const [toDate, setToDate] = useState<Date>();
  const [durationType, setDurationType] = useState<string>('full-day');
  const [reason, setReason] = useState<string>('');

  useEffect(() => {
    fetchLeaveData();
  }, []);

  const fetchLeaveData = async () => {
    try {
      setLoading(true);

      // Fetch balances and requests in parallel (no need for leave types in simplified system)
      const [balancesResponse, requestsResponse] = await Promise.all([
        apiService.getLeaveBalance(),
        apiService.getLeaveRequests()
      ]);

      if (balancesResponse.success && balancesResponse.data) {
        setLeaveBalances(balancesResponse.data.balances);
      }

      if (requestsResponse.success && requestsResponse.data) {
        setLeaveRequests(requestsResponse.data.requests);
      }
    } catch (error) {
      console.error('Error fetching leave data:', error);
      toast.error('Failed to load leave data');
    } finally {
      setLoading(false);
    }
  };

  const handleApplyLeave = async () => {
    if (!fromDate || !toDate || !reason.trim()) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (fromDate > toDate) {
      toast.error('From date cannot be after to date');
      return;
    }

    try {
      setSubmitting(true);

      const response = await apiService.applyLeave({
        // No leaveTypeId needed for simplified monthly system
        fromDate: fromDate.toISOString(),
        toDate: toDate.toISOString(),
        durationType,
        reason
      });

      if (response.success) {
        toast.success('Leave request submitted successfully');
        setShowApplyForm(false);
        resetForm();
        fetchLeaveData(); // Refresh data
      } else {
        toast.error(response.error?.message || 'Failed to submit leave request');
      }
    } catch (error) {
      console.error('Error applying leave:', error);
      toast.error('Failed to submit leave request');
    } finally {
      setSubmitting(false);
    }
  };

  const resetForm = () => {
    setFromDate(undefined);
    setToDate(undefined);
    setDurationType('full-day');
    setReason('');
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: AlertCircle },
      approved: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      rejected: { color: 'bg-red-100 text-red-800', icon: XCircle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center space-x-1`}>
        <Icon className="h-3 w-3" />
        <span className="capitalize">{status}</span>
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd/MM/yyyy');
  };

  const formatDateTime = (dateString: string) => {
    return format(new Date(dateString), 'dd/MM/yyyy, hh:mm a');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        <span className="ml-2 text-gray-500">Loading leave data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Leave Management</h1>
          <p className="text-gray-600 mt-1">Manage your leave requests and view balances</p>
          <p className="text-sm text-blue-600 mt-1">
            📍 All dates displayed in Indian Standard Time (IST)
          </p>
        </div>
        <Button 
          onClick={() => setShowApplyForm(true)}
          className="flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Apply for Leave</span>
        </Button>
      </div>

      {/* Leave Balances */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {leaveBalances.map((balance, index) => (
          <Card key={index}>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">{balance.leaveType}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-1">
                    {balance.remainingLeaves}
                  </div>
                  <div className="text-sm text-gray-600">Available Leaves</div>
                </div>
                <div className="border-t pt-3 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Monthly Credit:</span>
                    <span className="font-medium">1 leave/month</span>
                  </div>
                  {balance.carriedForward > 0 && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Carried Forward:</span>
                      <span className="font-medium text-blue-600">{balance.carriedForward}</span>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Leave Requests */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>My Leave Requests</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {leaveRequests.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No leave requests found</p>
              <p className="text-sm">Apply for your first leave request above</p>
            </div>
          ) : (
            <div className="space-y-4">
              {leaveRequests.slice(0, 10).map((request) => (
                <div key={request.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium">Leave Request</h4>
                      <p className="text-sm text-gray-600">
                        {formatDate(request.fromDate)} to {formatDate(request.toDate)} 
                        ({request.totalDays} day{request.totalDays !== 1 ? 's' : ''})
                      </p>
                    </div>
                    {getStatusBadge(request.status)}
                  </div>
                  <p className="text-sm text-gray-700 mb-2">{request.reason}</p>
                  <div className="flex justify-between items-center text-xs text-gray-500">
                    <span>Applied: {formatDateTime(request.appliedDate)}</span>
                    {request.approvedBy && (
                      <span>
                        {request.status === 'approved' ? 'Approved' : 'Processed'} by {request.approvedBy}
                        {request.approvedAt && ` on ${formatDateTime(request.approvedAt)}`}
                      </span>
                    )}
                  </div>
                  {request.comments && (
                    <div className="mt-2 p-2 bg-gray-50 rounded text-sm">
                      <strong>Comments:</strong> {request.comments}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Apply Leave Modal/Form */}
      {showApplyForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardHeader>
              <CardTitle>Apply for Leave</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                <div className="flex items-center space-x-2">
                  <Info className="h-4 w-4 text-blue-600" />
                  <span className="text-sm text-blue-800">
                    You get 1 leave credit per month. Unused leaves carry forward automatically.
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>From Date *</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {fromDate ? format(fromDate, 'dd/MM/yyyy') : 'Select date'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={fromDate}
                        onSelect={setFromDate}
                        disabled={(date) => date < new Date()}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div>
                  <Label>To Date *</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {toDate ? format(toDate, 'dd/MM/yyyy') : 'Select date'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={toDate}
                        onSelect={setToDate}
                        disabled={(date) => date < (fromDate || new Date())}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              <div>
                <Label htmlFor="durationType">Duration Type</Label>
                <Select value={durationType} onValueChange={setDurationType}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="full-day">Full Day</SelectItem>
                    <SelectItem value="half-day">Half Day</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="reason">Reason *</Label>
                <Textarea
                  id="reason"
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  placeholder="Please provide a reason for your leave request"
                  rows={3}
                />
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setShowApplyForm(false);
                    resetForm();
                  }}
                  disabled={submitting}
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleApplyLeave}
                  disabled={submitting}
                >
                  {submitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    'Submit Request'
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default EmployeeLeaveManagement;
