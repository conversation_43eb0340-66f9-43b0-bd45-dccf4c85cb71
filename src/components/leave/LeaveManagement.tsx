
import { useEffect, useMemo, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, Plus, Clock, CheckCircle, XCircle } from 'lucide-react';
import { toast } from 'sonner';
import apiService from '@/services/api';

interface LeaveBalanceUI { type: string; total: number; used: number; remaining: number; }
interface LeaveRequestUI { id: string; type: string; from: string; to: string; days: number; status: string; reason: string; }

// Fixed Select empty value issue - v2
const LeaveManagement = () => {
  const [showApplyForm, setShowApplyForm] = useState(false);
  const [balances, setBalances] = useState<LeaveBalanceUI[]>([]);
  const [requests, setRequests] = useState<LeaveRequestUI[]>([]);
  const [applying, setApplying] = useState(false);
  const [leaveTypes, setLeaveTypes] = useState<Array<{ id: string; name: string }>>([]);


  // Apply form state
  const [leaveTypeId, setLeaveTypeId] = useState<string>('annual'); // Default to a valid value
  const [duration, setDuration] = useState<string>('full-day');
  const [fromDate, setFromDate] = useState<string>('');
  const [toDate, setToDate] = useState<string>('');
  const [reason, setReason] = useState<string>('');

  const loadData = async () => {
    const [typesRes, balRes, reqRes] = await Promise.all([
      apiService.getLeaveTypes(),
      apiService.getLeaveBalance(),
      apiService.getLeaveRequests(),
    ]);

    if (typesRes.success && typesRes.data) {
      const types = (typesRes.data.types || []).map((t: any) => ({ id: t.id, name: t.name }));
      setLeaveTypes(types);
    } else if (!typesRes.success) {
      toast.error(typesRes.error?.message || 'Failed to load leave types');
    }

    if (balRes.success && balRes.data) {
      const ui = (balRes.data.balances || []).map((b: any) => ({
        type: b.leaveType,
        total: b.totalAllocated,
        used: b.usedLeaves,
        remaining: b.remainingLeaves,
      }));
      setBalances(ui);
    } else if (!balRes.success) {
      toast.error(balRes.error?.message || 'Failed to load leave balance');
    }

    if (reqRes.success && reqRes.data) {
      const uiReq = (reqRes.data.requests || []).map((r: any) => ({
        id: r.id,
        type: r.leaveType,
        from: new Date(r.fromDate).toISOString().slice(0, 10),
        to: new Date(r.toDate).toISOString().slice(0, 10),
        days: r.totalDays,
        status: r.status?.charAt(0).toUpperCase() + r.status?.slice(1),
        reason: r.reason,
      }));
      setRequests(uiReq);
    } else if (!reqRes.success) {
      toast.error(reqRes.error?.message || 'Failed to load leave requests');
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const handleSubmitLeave = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!leaveTypeId) {
      toast.error('Please select a leave type');
      return;
    }
    if (!fromDate || !toDate) {
      toast.error('Please select from/to dates');
      return;
    }

    setApplying(true);
    try {
      const payload = {
        leaveTypeId,
        fromDate: new Date(fromDate).toISOString(),
        toDate: new Date(toDate).toISOString(),
        durationType: duration,
        reason,
      };
      const res = await apiService.applyLeave(payload);
      if (res.success) {
        toast.success('Leave application submitted successfully');
        setShowApplyForm(false);
        // reset
        setLeaveTypeId('');
        setDuration('full-day');
        setFromDate('');
        setToDate('');
        setReason('');
        await loadData();
      } else {
        toast.error(res.error?.message || 'Failed to submit leave application');
      }
    } catch (err) {
      toast.error('Failed to submit leave application');
    } finally {
      setApplying(false);
    }
  };

  const leaveBalance = balances;
  const leaveHistory = requests;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Leave Management</h1>
          <p className="text-gray-600 mt-1">Manage your leave requests and track your leave balance</p>
        </div>
        <Button
          onClick={() => setShowApplyForm(!showApplyForm)}
          className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          Apply for Leave
        </Button>
      </div>

      {/* Apply Leave Form */}
      {showApplyForm && (
        <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>Apply for Leave</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmitLeave} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="leaveType">Leave Type</Label>
                  <Select value={leaveTypeId} onValueChange={setLeaveTypeId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select leave type" />
                    </SelectTrigger>
                    <SelectContent>
                      {leaveTypes.filter(t => t.id && t.id.trim() !== '').map((t) => (
                        <SelectItem key={t.id} value={t.id}>
                          {t.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="duration">Duration</Label>
                  <Select value={duration} onValueChange={setDuration}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select duration" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="half-day">Half Day</SelectItem>
                      <SelectItem value="full-day">Full Day</SelectItem>
                      <SelectItem value="multiple-days">Multiple Days</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="fromDate">From Date</Label>
                  <Input type="date" id="fromDate" value={fromDate} onChange={(e) => setFromDate(e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="toDate">To Date</Label>
                  <Input type="date" id="toDate" value={toDate} onChange={(e) => setToDate(e.target.value)} />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="reason">Reason</Label>
                <Textarea
                  id="reason"
                  placeholder="Please provide a reason for your leave request..."
                  rows={3}
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                />
              </div>

              <div className="flex space-x-4">
                <Button type="submit" className="bg-green-600 hover:bg-green-700" disabled={applying}>
                  Submit Application
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowApplyForm(false)}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Leave Balance */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {leaveBalance.map((leave, index) => (
          <Card key={index} className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="text-center">
                <h3 className="font-semibold text-gray-900 mb-2">{leave.type}</h3>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-blue-600">{leave.remaining}</div>
                  <div className="text-sm text-gray-600">Remaining</div>
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Used: {leave.used}</span>
                    <span>Total: {leave.total}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full"
                      style={{ width: `${leave.total ? (leave.used / leave.total) * 100 : 0}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Leave History */}
      <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Leave History</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Leave Type</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">From</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">To</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Days</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Reason</th>
                </tr>
              </thead>
              <tbody>
                {leaveHistory.map((leave) => (
                  <tr key={leave.id} className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                    <td className="py-3 px-4 text-gray-900">{leave.type}</td>
                    <td className="py-3 px-4 text-gray-700">{leave.from}</td>
                    <td className="py-3 px-4 text-gray-700">{leave.to}</td>
                    <td className="py-3 px-4 text-gray-700">{leave.days} day{leave.days > 1 ? 's' : ''}</td>
                    <td className="py-3 px-4">
                      <Badge
                        variant={
                          leave.status === 'Approved' ? 'default' :
                          leave.status === 'Pending' ? 'secondary' :
                          'destructive'
                        }
                        className={
                          leave.status === 'Approved' ? 'bg-green-100 text-green-800' :
                          leave.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }
                      >
                        {leave.status === 'Approved' && <CheckCircle className="h-3 w-3 mr-1" />}
                        {leave.status === 'Pending' && <Clock className="h-3 w-3 mr-1" />}
                        {leave.status === 'Rejected' && <XCircle className="h-3 w-3 mr-1" />}
                        {leave.status}
                      </Badge>
                    </td>
                    <td className="py-3 px-4 text-gray-700 max-w-xs truncate">{leave.reason}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default LeaveManagement;
