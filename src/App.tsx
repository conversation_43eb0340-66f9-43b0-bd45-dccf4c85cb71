
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider, useAuth } from "@/contexts/AuthContext";
import LoginPage from "@/components/auth/LoginPage";
import ProtectedRoute from "@/components/auth/ProtectedRoute";

import MainLayout from "@/components/layout/MainLayout";
import NotFound from "./pages/NotFound";

// Import all page components
import SuperAdminDashboard from "@/components/admin/SuperAdminDashboard";
import OrganizationsPage from "@/components/admin/OrganizationsPage";
import AnalyticsPage from "@/components/admin/AnalyticsPage";
import SystemSettingsPage from "@/components/admin/SystemSettingsPage";

import OrganizationAdminDashboard from "@/components/admin/OrganizationAdminDashboard";
import EmployeeManagement from "@/components/admin/EmployeeManagement";
import AdminAttendanceManagement from "@/components/admin/AdminAttendanceManagement";
import AdminLeaveManagement from "@/components/admin/AdminLeaveManagement";
import AdminTaskManagement from "@/components/admin/AdminTaskManagement";
import PerformanceManagement from "@/components/performance/PerformanceManagement";
import PayrollManagement from "@/components/payroll/PayrollManagement";
import RecruitmentPortal from "@/components/recruitment/RecruitmentPortal";
import BillingSubscription from "@/components/admin/BillingSubscription";

import EmployeeDashboard from "@/components/employee/EmployeeDashboard";
import AttendanceManagement from "@/components/attendance/AttendanceManagement";
import LeaveManagement from "@/components/leave/LeaveManagement";
import TaskManagement from "@/components/tasks/TaskManagement";
import EmployeeProfile from "@/components/employee/EmployeeProfile";
import EmployeeDetailsPage from "@/components/employee/EmployeeDetailsPage";

const queryClient = new QueryClient();



const AuthWrapper = () => {
  const { isAuthenticated, isLoading } = useAuth();

  // Show loading spinner while checking authentication state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <LoginPage onRegisterClick={() => {}} />;
  }

  return (
    <BrowserRouter>
      <Routes>
        {/* Default redirect to dashboard */}
        <Route path="/" element={<Navigate to="/dashboard" replace />} />

        {/* Dashboard routes */}
        <Route path="/dashboard" element={<MainLayout><DashboardRouter /></MainLayout>} />

        {/* Super Admin routes */}
        <Route path="/super-admin/organizations" element={
          <MainLayout>
            <ProtectedRoute allowedRoles={['super_admin']} fallbackPath="/dashboard">
              <OrganizationsPage />
            </ProtectedRoute>
          </MainLayout>
        } />
        <Route path="/super-admin/analytics" element={
          <MainLayout>
            <ProtectedRoute allowedRoles={['super_admin']} fallbackPath="/dashboard">
              <AnalyticsPage />
            </ProtectedRoute>
          </MainLayout>
        } />
        <Route path="/super-admin/system-settings" element={
          <MainLayout>
            <ProtectedRoute allowedRoles={['super_admin']} fallbackPath="/dashboard">
              <SystemSettingsPage />
            </ProtectedRoute>
          </MainLayout>
        } />

        {/* Organization Admin routes */}
        <Route path="/org-admin/employee-management" element={
          <MainLayout>
            <ProtectedRoute allowedRoles={['org_admin']} fallbackPath="/dashboard">
              <EmployeeManagement />
            </ProtectedRoute>
          </MainLayout>
        } />
        <Route path="/org-admin/attendance" element={
          <MainLayout>
            <ProtectedRoute allowedRoles={['org_admin']} fallbackPath="/dashboard">
              <AdminAttendanceManagement />
            </ProtectedRoute>
          </MainLayout>
        } />
        <Route path="/org-admin/leave" element={
          <MainLayout>
            <ProtectedRoute allowedRoles={['org_admin']} fallbackPath="/dashboard">
              <AdminLeaveManagement />
            </ProtectedRoute>
          </MainLayout>
        } />
        <Route path="/org-admin/tasks" element={
          <MainLayout>
            <ProtectedRoute allowedRoles={['org_admin']} fallbackPath="/dashboard">
              <AdminTaskManagement />
            </ProtectedRoute>
          </MainLayout>
        } />
        <Route path="/org-admin/performance" element={
          <MainLayout>
            <ProtectedRoute allowedRoles={['org_admin']} fallbackPath="/dashboard">
              <PerformanceManagement />
            </ProtectedRoute>
          </MainLayout>
        } />
        <Route path="/org-admin/payroll" element={
          <MainLayout>
            <ProtectedRoute allowedRoles={['org_admin']} fallbackPath="/dashboard">
              <PayrollManagement />
            </ProtectedRoute>
          </MainLayout>
        } />
        <Route path="/org-admin/recruitment" element={
          <MainLayout>
            <ProtectedRoute allowedRoles={['org_admin']} fallbackPath="/dashboard">
              <RecruitmentPortal />
            </ProtectedRoute>
          </MainLayout>
        } />
        <Route path="/org-admin/billing" element={
          <MainLayout>
            <ProtectedRoute allowedRoles={['org_admin']} fallbackPath="/dashboard">
              <BillingSubscription />
            </ProtectedRoute>
          </MainLayout>
        } />

        {/* Employee routes */}
        <Route path="/employee/attendance" element={
          <MainLayout>
            <ProtectedRoute allowedRoles={['employee']} fallbackPath="/dashboard">
              <AttendanceManagement />
            </ProtectedRoute>
          </MainLayout>
        } />
        <Route path="/employee/leave" element={
          <MainLayout>
            <ProtectedRoute allowedRoles={['employee']} fallbackPath="/dashboard">
              <LeaveManagement />
            </ProtectedRoute>
          </MainLayout>
        } />
        <Route path="/employee/tasks" element={
          <MainLayout>
            <ProtectedRoute allowedRoles={['employee']} fallbackPath="/dashboard">
              <TaskManagement />
            </ProtectedRoute>
          </MainLayout>
        } />
        <Route path="/employee/performance" element={
          <MainLayout>
            <ProtectedRoute allowedRoles={['employee']} fallbackPath="/dashboard">
              <PerformanceManagement />
            </ProtectedRoute>
          </MainLayout>
        } />
        <Route path="/employee/payroll" element={
          <MainLayout>
            <ProtectedRoute allowedRoles={['employee']} fallbackPath="/dashboard">
              <PayrollManagement />
            </ProtectedRoute>
          </MainLayout>
        } />
        <Route path="/employee/details" element={
          <MainLayout>
            <ProtectedRoute allowedRoles={['employee']} fallbackPath="/dashboard">
              <EmployeeDetailsPage />
            </ProtectedRoute>
          </MainLayout>
        } />

        {/* Shared routes */}
        <Route path="/profile" element={
          <MainLayout>
            <EmployeeProfile />
          </MainLayout>
        } />

        {/* 404 route */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </BrowserRouter>
  );
};

// Dashboard Router Component
const DashboardRouter = () => {
  const { user } = useAuth();

  switch (user?.role) {
    case 'super_admin':
      return <SuperAdminDashboard />;
    case 'org_admin':
      return <OrganizationAdminDashboard />;
    case 'employee':
      return <EmployeeDashboard />;
    default:
      return <Navigate to="/" replace />;
  }
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <AuthProvider>
        <Toaster />
        <Sonner />
        <AuthWrapper />
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
